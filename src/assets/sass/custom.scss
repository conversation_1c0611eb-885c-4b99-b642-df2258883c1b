@media print {
  ::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }

  .no-print {
    visibility: hidden;
    height: 0;
  }
}

.popover.tooltip-info {
  background: var(--kt-gray-900);
  border-color: var(--kt-gray-900);
  font-family: 'Inter', sans-serif;

  .popover-body {
    text-align: center;
    color: white;
  }

  &.bs-popover-top,
  &.bs-popover-auto[data-popper-placement^=top]  {
    > .popover-arrow::before {
      border-top-color: var(--kt-gray-900)
    }
  }

  //.bs-popover-bottom > .popover-arrow::before,
  // .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {}
  &.bs-popover-bottom,
  &.bs-popover-auto[data-popper-placement^=bottom] {
    > .popover-arrow::after {
      border-bottom-color: var(--kt-gray-900);
    }
  }
}

.on_copy_password {
  display: flex;
  gap: 10px !important;

  .popover {
    border-radius: 40px !important;
  }
}

.info-header-detail-products {
  .tooltip-info-product {
    background-color: #353535;


    .popover-body {
      text-align: center;
      color: white !important;
    }
  }
}

.swiper-body-product-detail {
  .swiper-slide {
    width: 60px !important;
  }
}

.popover[class*=tour-] {
  background: #434A54;
  color: white;
}

.loading_edit_product {
  margin-top: 3rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3rem;

  .loader_custom {
    .skeleton-loader.progress {
      margin: 10px !important;
    }
  }
}


.input-password-custom {
  padding-left: 80px !important;
}

.hidden-showing-rows {
  .mat-paginator-page-size {
    display: none !important;
  }

  .mat-paginator-range-label {
    display: none !important;
  }
}

.bottom_paginated_transparent_variant_information {


  .mat-paginator-container {
    background-color: #f4f4f4 !important;
  }

  .mat-paginator-page-size {
    display: none !important;
  }

  .mat-paginator-range-label {
    display: none !important;
  }

}

.block-ellipsis-customs {
  display: -webkit-box;
  max-width: 100%;
  margin: 0 auto;
  font-size: 14px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.font-10 {
  font-size: 10px;
}

.font-12 {
  font-size: 12px;
}

.font-14 {
  font-size: 14px;
}

.font-16 {
  font-size: 16px;
}

.font-20 {
  font-size: 20px;
}

.form .btn {
  &:disabled {
    filter: grayscale(1);
  }
}

// mat-input-element
  .mat-mdc-input-element  {
    background-color: var(--kt-gray-100);
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    height: 50px;
    color: #353535;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-radius: 6px;
    box-sizing: border-box;
    border: 1px solid #f8f8f8;

    &::placeholder {
      color: var(--kt-gray-300);
    }
  }
