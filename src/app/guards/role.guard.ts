import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { BehaviorSubject, shareReplay } from 'rxjs';
import { AuthService } from '@services/auth.service';

import { IPrivileges, IRolePrivilege } from '../pages/adminsettings/role/role.interface';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class RoleGuard {
  rolePrivilegeSubject = new BehaviorSubject<IRolePrivilege>({} as IRolePrivilege);

  // hasAccessPageSubject = new BehaviorSubject<boolean>(false);

  constructor(private router: Router, private authService: AuthService, private rolePrivilegeService: RolePrivilegeService) {}

  canActivateChild(route: ActivatedRouteSnapshot) {
    return this.checkUserAccess(route);
  }

  checkUserAccess(route: ActivatedRouteSnapshot) {
    if (this.authService.isLoggedIn()) {
      const _privilegeData: IPrivileges[] = this.rolePrivilegeService.getDataUserPrivileges();

      if (!_privilegeData) {
        this.fetchUserPrivileges().subscribe((res) => {
          return this.setPrivilege(res, route);
        });
      }

      return this.setPrivilege(_privilegeData, route);
    }

    this.router.navigate(['/error/404']).then();
    return false;
  }

  setPrivilege(data: IPrivileges[], route: ActivatedRouteSnapshot) {
    // let _hasAccessPage: boolean;
    // let _rolePrivilege: IRolePrivilege;
    const _enumAccess = route.routeConfig?.data ? route.routeConfig.data.enum : route.data.enum;

    const _rolePrivilege = {
      role: this.authService.getRoleFromToken(),
      privilege: data,
    };

    this.rolePrivilegeSubject.next(_rolePrivilege);
    const { privilege } = this.rolePrivilegeSubject.value;

    // let _itemEnumFound;
    const _itemEnumFound = privilege.map((item) => {
      const { name, child } = item;
      const _hasChild = !!child.length;

      if (name === _enumAccess) {
        return item;
      } else if (name !== _enumAccess && _hasChild) {
        return this.findEnumInChild(child, _enumAccess);
      }
    });

    const _EnumFound = _itemEnumFound.filter((item) => item !== undefined);
    const _hasAccessPage = !!_EnumFound.length;

    if (!_hasAccessPage) {
      this.router.navigate(['/admin-cms']).then();
      return false;
    }

    return true;
  }

  findEnumInChild(_child: any[], _enum: string) {
    let _findInChild;

    for (const item of _child) {
      const { child } = item;
      const _hasChildren = !!child.length;

      if (item.name === _enum) {
        _findInChild = item;
      }

      if (_hasChildren) {
        child.filter((childItem: { name: string }) => {
          if (childItem.name === _enum) {
            _findInChild = childItem;
          }
        });
      }
    }

    return _findInChild;
  }

  fetchUserPrivileges() {
    const userPrivilege$ = this.rolePrivilegeService.getUserPrivileges();
    return userPrivilege$.pipe(
      shareReplay(),
      map((resp) => {
        let _privilege = [] as IPrivileges[];

        if (resp && resp.success) {
          const { privilege } = resp.data;
          _privilege = privilege;
        }

        return _privilege;
      })
    );
  }
}
