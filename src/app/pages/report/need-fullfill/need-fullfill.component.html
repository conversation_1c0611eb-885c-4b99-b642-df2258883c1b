<app-card [header]="true">
  <ng-container cardHeader>
    <app-input-search
      (actionFilter)="onSearch($event)"
      [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject"
      placeholder="Silahkan Cari Nama produk/distributor"
      value="{{ string_filter }}"
    ></app-input-search>
  </ng-container>

  <ng-container cardBody class="mb-8">
    <div class="table-responsive">
      <table [dataSource]="baseDataSource" (matSortChange)="sortTable($event)" mat-table matSort class="table w-100 gy-5 table-row-bordered align-middle">
        <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
          <!-- COLUMN HEADER -->
          <ng-container *ngIf="tableColumn.isSortable; else notSortableHeader">
            <th
              *matHeaderCellDef
              [arrowPosition]="'after'"
              [mat-sort-header]="tableColumn.key"
              class="min-w-125px px-3"
              [ngStyle]="{ width: tableColumn.key === 'qty_selling' || tableColumn.key === 'qty_bottle' || tableColumn.key === 'created_date' ? '110px' : 'auto' }"
              mat-header-cell
            >
              <app-table-content [type]="'text'" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject">
                <div class="text-start">{{ tableColumn.title }}</div>
              </app-table-content>
            </th>
          </ng-container>
          <ng-template #notSortableHeader>
            <th *matHeaderCellDef mat-header-cell class="min-w-125px px-3" [ngStyle]="{ width: tableColumn.key === 'qty_po' ? '110px' : 'auto' }">
              <app-table-content [type]="'text'" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject">
                {{ tableColumn.title }}
              </app-table-content>
            </th>
          </ng-template>

          <!-- COLUMN DATA -->
          <td *matCellDef="let element" class="px-3" mat-cell>
            <ng-container [ngSwitch]="tableColumn.key">
              <div *ngSwitchCase="'distributor_name'">
                <app-table-content [count]="1" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject" [type]="'text'">
                  <span class="block-ellipsis-customs">
                    {{ element['distributor_name'] }}
                  </span>
                </app-table-content>
              </div>

              <div *ngSwitchCase="'product_name'">
                <app-table-content [count]="1" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject" [type]="'text'">
                  <span class="block-ellipsis-customs">
                    {{ element['product_name'] }}
                  </span>
                </app-table-content>
              </div>

              <ng-container *ngSwitchCase="'amount'">
                <app-table-content [count]="1" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject" [type]="'text'">
                  <span>Rp{{ utilitiesService.toRupiah(element['amount'], true, false) }}</span>
                </app-table-content>
              </ng-container>

              <ng-container *ngSwitchCase="'created_date'">
                <app-table-content [count]="2" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject" [type]="'text'">
                  <div>{{ utilitiesService.timeStampToDate(element['created_date'], 'dd-MM-yyyy') }}</div>
                  <div>{{ utilitiesService.timeStampToDate(element['created_date'], 'HH:mm:ss') }}</div>
                </app-table-content>
              </ng-container>

              <ng-container *ngSwitchDefault>
                <app-table-content [count]="1" [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject" [type]="'text'">
                  <span *ngIf="tableColumn.key === 'qty_bottle' || tableColumn.key === 'qty_po' || tableColumn.key === 'qty_selling'; else defaultValue">
                    {{ utilitiesService.toThousandConvert(element[tableColumn.key]) }}
                  </span>
                  <ng-template #defaultValue>
                    <span>{{ element[tableColumn.key] }}</span>
                  </ng-template>
                </app-table-content>
              </ng-container>
            </ng-container>
          </td>
        </ng-container>

        <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>

    <div class="d-flex justify-content-between py-4">
      <app-mai-material-bottom-table
        (changePage)="changePageEvent($event)"
        [baseDataTableComponent]="baseDataSource"
        [isFinishLoadingSubject]="baseDataSource.isFinishLoadingSubject"
        class="w-100"
      ></app-mai-material-bottom-table>
    </div>

    <ng-container *ngIf="(baseDataSource.finishLoading | async) && baseDataSource.totalItem$.value < 1">
      <app-card-empty text="Belum terdapat data."></app-card-empty>
    </ng-container>
  </ng-container>
</app-card>
