import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject, Subscription } from 'rxjs';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ActivatedRoute } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { IDetailMonitoringFA } from '@pages/monitoring-fa/monitoring.interface';

@Component({
  selector: 'app-detail-monitoring-fa',
  templateUrl: './detail-monitoring-fa.component.html',
  styleUrl: './detail-monitoring-fa.component.scss',
})
export class DetailMonitoringFaComponent implements OnInit, OnDestroy {
  ASSET_ICON = STRING_CONSTANTS.ICON;

  id: string | null = null;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  detailSubject: BehaviorSubject<IDetailMonitoringFA> = new BehaviorSubject<IDetailMonitoringFA>({} as IDetailMonitoringFA);

  links: PageLink[] = [
    {
      title: 'Monitoring',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'List Report Kegiatan FA',
      path: '/monitoring/monitoring-fa/list',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'Detail Monitoring',
      path: '',
      isActive: true,
    },
  ];

  private unsubscribe: Subscription[] = [];

  get DataValue() {
    return this.detailSubject.value;
  }

  codeRetailerDanCodeKegiatan(iDetailMonitoring: IDetailMonitoringFA) {
    if (iDetailMonitoring.activity_code !== undefined && iDetailMonitoring.activity_code !== null) {
      return iDetailMonitoring.retailer_code + ' Activity Code ' + iDetailMonitoring.activity_code;
    }
    return iDetailMonitoring.retailer_code + ' ';
  }

  constructor(
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    public utilitiesService: UtilitiesService,
    private baseService: BaseService,
  ) {
    this.id = this.activeRoute.snapshot.paramMap.get('id');
  }

  ngOnInit() {
    this.initPageInfo();
    this.getDetail();
  }

  initPageInfo() {
    // this.pageInfoService.setPageTitleDescription(this.activeRoute.snapshot.data);
    this.pageInfoService.updateTitle('Detail Monitoring');
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  getDetail() {
    this.isLoading.next(true);
    this.baseService.getData<IDetailMonitoringFA>(API.MONITORING.GET_DETAIL_DAILY + this.id).subscribe((resp) => {
      if (resp && resp.data) {
        this.detailSubject.next(resp.data);
        this.isLoading.next(false);
      }
    });
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
