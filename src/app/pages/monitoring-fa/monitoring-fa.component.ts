import { <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, Component, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { TableColumn } from '@shared/interface/table.interface';
import { BehaviorSubject, Subscription } from 'rxjs';
import { BaseDatasource } from '@shared/base/base.datasource';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatChipListbox, MatChipListboxChange } from '@angular/material/chips';
import { IGenericIdName, IGenericLabelValue } from '@shared/interface/generic';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ActivatedRoute, ActivatedRouteSnapshot, Router } from '@angular/router';
import { FilterService } from '@services/filter.service';
import { UtilitiesService } from '@services/utilities.service';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UrlUtilsService } from '@utils/url-utils.service';
import { IMonitoringFAList } from '@pages/monitoring-fa/monitoring.interface';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';
import { MonitoringCategoryEnum } from '@pages/monitoring-fa/monitoring.enum';
import { EnumProgramMarketingTypeString } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';

@Component({
  selector: 'app-monitoring-fa',
  templateUrl: './monitoring-fa.component.html',
  styleUrl: './monitoring-fa.component.scss',
})
export class MonitoringFaComponent implements OnInit, AfterViewInit, OnDestroy {
  ASSET_ICON = STRING_CONSTANTS.ICON;

  string_filter?: string;

  displayedColumns!: string[];
  tableColumns: TableColumn[] = [];
  baseDatasource!: BaseDatasource<IMonitoringFAList>;

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  isOpenFilter = false;
  isActiveFilter = false;
  filterForm!: FormGroup;
  filterPillsBox!: MatChipListbox;
  currentDate = new Date();
  filterPills: IGenericLabelValue[] = [];
  activeRouteSnapshot!: ActivatedRouteSnapshot;

  optionFA: InputSelectInterface[] = [];
  selectedEmployee: string = '';

  bcLinks: PageLink[] = [
    {
      title: 'Monitoring',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'List Report Kegiatan FA',
      path: '',
      isActive: true,
    },
  ];

  @ViewChild('filterPillsBoxCategory') filterPillsBoxCategory: MatChipListbox;

  get CategoryControl() {
    return <FormControl>this.filterForm.get('category');
  }

  get EmployeeControl() {
    return <FormControl>this.filterForm.get('employee_id');
  }

  private unsubscribe: Subscription[] = [];

  constructor(
    private activeRoute: ActivatedRoute,
    private router: Router,
    private pageInfoService: PageInfoService,
    private filterService: FilterService,
    public utilitiesService: UtilitiesService,
    private baseTable: BaseTableService<IMonitoringFAList>,
    private paginationService: UrlUtilsService,
    private baseService: BaseService,
    private fb: FormBuilder
  ) {}

  ngOnInit() {
    this.activeRouteSnapshot = this.activeRoute.snapshot;
    this.setTableData();
    this.initPageInfo();
    this.initFilter();
    this.queryHandler();
    setTimeout(() => {
      this.isLoading.next(false);
    }, 150);
  }

  ngAfterViewInit() {
    this.initFilterPillsBox();
  }

  initFilterPillsBox() {
    const { queryParams } = this.activeRouteSnapshot;
    const queryParamIsEmpty = !Object.keys(queryParams).length;

    if (queryParamIsEmpty) return;

    const _arrayType = 'type' in queryParams ? queryParams.type.split(',') : [];

    const _selectedCategory: string[] = [];

    if (!!_arrayType.length) {
      _arrayType.forEach((_type: string) => {
        const value = this.utilitiesService.mapKeyToString(EnumProgramMarketingTypeString, _type);
        _selectedCategory.push(value);
      });

      this.filterPillsBoxCategory.value = _selectedCategory;
      this.CategoryControl.patchValue(_selectedCategory);
    }
  }

  initFilter() {
    this.filterForm = this.fb.group({
      category: [''],
      employee_id: new FormControl({ value: '', disabled: false }),
    });
    this.getCategory();
    this.getOptionFA();
  }

  setTableData() {
    const _dataSubs = this.baseTable.responseDatabase.subscribe((resp) => (this.baseDatasource = resp));

    this.tableColumns = [
      {
        key: 'retailer_code',
        title: 'Kode Retailer',
        isSortable: false,
      },
      {
        key: 'activity_code',
        title: 'Code Aktivitas',
        isSortable: false,
      },
      {
        key: 'fa_name',
        title: 'FA Bertugas',
        isSortable: false,
      },
      {
        key: 'activity_category_name',
        title: 'Kategori',
        isSortable: false,
      },
      {
        key: 'createdDate',
        title: 'Tanggal Aktivitas',
        isSortable: false,
      },
      {
        key: 'actions',
        title: 'action',
      },
    ];
    this.displayedColumns = this.tableColumns.map((head) => head.key);

    this.unsubscribe.push(_dataSubs);
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      this.filterForm.controls['category'].setValue(data.category);
      this.filterForm.controls['employee_id'].setValue(data.employee_id);
      this.isActiveFilter = !!(data.category || data.employee_id);
      const param = this.paginationService.sliceQueryParams();
      if (param) {
        this.baseTable.loadDataTable(API.MONITORING.DAILY_LIST_REQUEST, param);
      } else {
        this.baseTable.loadDataTable(API.MONITORING.DAILY_LIST_REQUEST, '');
      }
    });
  }

  // queryHandler() {
  //   this.activeRoute.queryParams.subscribe((data) => {
  //     const { string_filter, category, employee_id } = data;
  //
  //     this.string_filter = string_filter;
  //     this.isActiveFilter = !!(category || employee_id);
  //     const param = this.paginationService.sliceQueryParams() ?? '';
  //     this.baseTable.loadDataTable(API.MONITORING.DAILY_LIST_REQUEST, param);
  //   });
  // }

  initPageInfo() {
    // this.pageInfoService.setPageTitleDescription(this.activeRoute.snapshot.data);
    this.pageInfoService.updateBreadcrumbs(this.bcLinks);
  }

  onSearch(event: string) {
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  async handleActionDetail(element: any) {
    return this.router.navigate([`monitoring/detail/${element.id}`], {
      state: { data: element },
    });
  }

  changePageEvent($event: BaseDatasource<IMonitoringFAList>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  handleOpenFilter = () => {
    this.isOpenFilter = !this.isOpenFilter;
  };

  handleResetFilter = () => {
    this.isActiveFilter = false;
    this.isOpenFilter = false;
    const _keyFilter = ['category', 'employee_id'];
    this.filterService.resetFilter(this.filterForm, _keyFilter);
    this.resetFilterPillsBox();
  };

  resetFilterPillsBox = () => (this.filterPillsBoxCategory.value = []);

  handleSubmitFilter = () => {
    this.isActiveFilter = true;
    this.isOpenFilter = false;

    // this.checkActive.next(!this.checkActive.value);
    const _extras = {
      queryParams: {
        string_filter: this.string_filter,
        employee_id: this.EmployeeControl.value ?? undefined,
        category: this.CategoryControl.value ?? undefined,
      },
    };

    this.filterService.submitFilter(this.filterForm, _extras);
  };

  handleCategoryChange(e: MatChipListboxChange) {
    const _selectedKey: string[] = [];
    e.value.forEach((item: IGenericLabelValue) => {
      return _selectedKey.push(this.utilitiesService.getEnumKeyByValue(MonitoringCategoryEnum, item.value));
    });
    this.CategoryControl.patchValue(_selectedKey.join());
  }

  getCategory() {
    this.baseService.getData<IGenericIdName[]>(API.MONITORING.FILTER_CATEGORY).subscribe((resp) => {
      if (resp && resp.data) {
        this.mapCategory(resp.data);
      }
    });
  }

  getOptionFA() {
    this.baseService.getData<IGenericIdName[]>(API.MONITORING.GET_LIST_FA).subscribe((value) => {
      if (value) {
        this.optionFA = value.data.map((value1) => {
          const inputSelector: InputSelectInterface = new InputSelectInterface();
          inputSelector.value = value1.id;
          inputSelector.display = value1.name;
          return inputSelector;
        });
      }
    });
  }

  mapCategory(data: IGenericIdName[]) {
    if (!data.length) return;

    data.map((item) => {
      this.filterPills.push({ value: item.id, label: item.name });
    });
  }

  handleChangeEmployee(event: string) {
    this.selectedEmployee = event;
  }

  validateFilter() {
    return !(!!this.CategoryControl.value || !!this.EmployeeControl.value);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
