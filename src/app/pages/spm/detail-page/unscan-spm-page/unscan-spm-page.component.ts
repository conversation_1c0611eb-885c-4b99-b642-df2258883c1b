import { Component, OnInit, ViewChild } from '@angular/core';
import { ScannerUiInputComponent } from '@shared/components/scanner-ui-input/scanner-ui-input.component';
import { BehaviorSubject } from 'rxjs';
import { IListProductScanResponse } from '@pages/sales-order/detail-sales-order/scan-products/scanproducts.interface';
import { IDetailScanSpm, ISpmProduct } from '@models/spm.model';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseService } from '@services/base-service.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { API } from '@config/constants/api.constant';
import { CustomsSnackbarComponent } from '@shared/components/customs-snackbar/customs-snackbar.component';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-unscan-spm-page',
  templateUrl: './unscan-spm-page.component.html',
  styleUrls: ['./unscan-spm-page.component.scss'],
})
export class UnscanSpmPageComponent implements OnInit {
  @ViewChild('submitScan') uiScanner: ScannerUiInputComponent;
  @ViewChild('inputResetScan') inputResetScan: ScannerUiInputComponent;
  @ViewChild('inputResetScanInPage') inputResetScanInPage: ScannerUiInputComponent;
  @ViewChild('inputResetScanInModal') inputResetScanInModal: ScannerUiInputComponent;

  productId: string;
  spmId: string;
  status: string;

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  isSuccessScanCount: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  productNameScan: BehaviorSubject<string> = new BehaviorSubject<string>('');
  links: Array<PageLink>;

  modalConfigResetScanOne: ModalConfig = {
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Input Manual',
    onClose: () => {
      this.inputResetScanInPage.inputQrForm.first.nativeElement.focus();
      return true;
    },
    onDismiss: () => this.onResetScanDismiss(),
  };

  modalConfigResetScanManual: ModalConfig = {
    modalTitle: 'Input QR Manual',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Submit',
    onDismiss: () => this.onResetScanManual(),
  };

  modalInputManualConfig: ModalConfig = {
    modalTitle: 'INPUT QR MANUAL',
    closeButtonLabel: 'Close',
    dismissButtonLabel: 'Submit',
  };

  modalConfigResetScan: ModalConfig = {
    modalTitle: 'RESET SCAN QR',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Lanjutkan',
    color: 'danger',
  };

  @ViewChild('modalInputManual') private modalInputManual: ModalComponent;
  @ViewChild('modalResetScan') private modalResetScan: ModalComponent;
  @ViewChild('modalResetScanOne') private modalResetScanOne: ModalComponent;
  @ViewChild('modalResetScanManual') private modalResetScanManual: ModalComponent;

  detailProductScan = new BehaviorSubject({} as IDetailScanSpm);
  productScan = new BehaviorSubject({} as ISpmProduct);
  STRING_CONSTANTS = STRING_CONSTANTS;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private baseService: BaseService,
    private snackBar: MatSnackBar,
    private pageInfoService: PageInfoService
  ) {}

  ngOnInit(): void {
    this.initPageInfo();
    this.getData();
    this.updateBreadcrumbScan();
    this.productScanSubs();
  }

  initPageInfo() {
    const { spm, status, product } = this.activatedRoute.snapshot.params;

    this.spmId = spm;
    this.status = status;
    this.productId = product;

    this.pageInfoService.updateTitle(`Reset Scan Produk`, undefined, false);
  }

  getData() {
    this.isLoading.next(true);
    this.baseService.getData<IDetailScanSpm>(`${API.SPM.GET_DETAIL_SCAN}${this.spmId}/${this.productId}`).subscribe((resp) => {
      if (!resp) return;
      this.detailProductScan.next(resp.data);
      this.productScan.next(resp.data.product_scan);
      this.isLoading.next(false);
    });
  }

  updateBreadcrumbScan() {
    this.detailProductScan.subscribe((res) => {
      if (!Object.keys(res).length) return;
      const { surat_perintah_muat_code } = res;
      this.links = [
        { title: 'Surat Perintah Muat', path: '/spm/list', isActive: false },
        { title: '', path: '', isActive: false, isSeparator: true },
        { title: 'List SPM', path: '/spm/' + this.status, isActive: false },
        { title: '', path: '', isActive: false, isSeparator: true },
        { title: surat_perintah_muat_code ?? '', path: '/spm/' + this.status + '/' + this.spmId, isActive: false },
        { title: '', path: '', isActive: false, isSeparator: true },
        { title: 'Reset Scan Produk', path: '', isActive: true },
      ];

      this.pageInfoService.updateBreadcrumbs(this.links);
    });
  }

  productScanSubs() {
    this.productScan.subscribe((item) => {
      if (!!Object.keys(item).length) {
        this.checkSuccessScan(item);
      }
    });
  }

  checkSuccessScan = (data: IListProductScanResponse) => this.isSuccessScanCount.next(data.total_scanned >= data.total_package);

  updateScanner($event: IListProductScanResponse) {
    if ($event && !!Object.keys($event).length) this.productScan.next($event as ISpmProduct);
  }

  onClickSubmitModal = () => {
    this.uiScanner.submit();
  };

  handleAction(scanned: number, name: string) {
    if (scanned > 0) {
      this.productNameScan.next(name);
      this.modalResetScan.open().then();
    }
  }

  cancelScanAction = () => {
    this.modalResetScan.close().then();
  };

  resetScanActions = () => {
    this.modalResetScan.close().then();
    this.baseService.putData(API.RESET_SCAN_SPM_PRODUCTS + this.spmId, { product_id: this.productId }).subscribe((res) => {
      if (res) {
        this.getData();
        this.snackbarResponse(res.message);
      }
    });
  };

  onResetScanManual() {
    this.inputResetScan.submit(true)?.then((eventSubs) => {
      eventSubs.subscribe((resp) => {
        if (resp) {
          this.inputResetScan.isLoading.next(false);
          const { message } = resp;
          this.getData();
          this.snackbarResponse(message);
        }
      });
    });

    return true;
  }

  onResetScanDismiss() {
    return this.modalResetScanOne.close().then(() => {
      return this.modalResetScanManual.open();
    });
  }

  snackbarResponse(message: string) {
    return this.snackBar.openFromComponent(CustomsSnackbarComponent, {
      duration: 3000,
      horizontalPosition: 'end',
      data: {
        color: '#353535',
        text: message,
        icon: STRING_CONSTANTS.ICON.IC_BULLET_TICK_WHITE,
      },
    });
  }

  updateScannerOne($event: any) {
    this.snackbarResponse($event);
    this.getData();
  }

  getInfoUnscanText(data: ISpmProduct = this.detailProductScan.value.product_scan) {
    const { qty_need_to_unscan, unit } = data;
    return `Silahkan unscan sebanyak ${qty_need_to_unscan} ${unit} untuk dapat menyimpan SPM.`;
  }

  backBtnClick = () => this.router.navigate(['/spm/' + this.status + '/' + this.spmId]);
}
