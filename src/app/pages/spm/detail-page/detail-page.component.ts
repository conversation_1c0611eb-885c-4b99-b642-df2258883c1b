import {Component, OnInit} from '@angular/core';
import {ITabList} from '@shared/interface/tablist.interface';
import {ActivatedRoute} from '@angular/router';
import {UtilitiesService} from '@services/utilities.service';
import {PageInfoService} from '@metronic/layout';
import {TabListLabelDetailSpm} from '../enum/tablist-label.enum';
import {StatusSPM} from '../submenu.enum';
import {STRING_CONSTANTS} from '@config/constants/string.constants';
import {SpmService} from "../spm.service";

@Component({
  selector: 'app-detail-sales-order',
  templateUrl: './detail-page.component.html',
  styleUrls: ['./detail-page.component.scss'],
})
export class DetailPageComponent implements OnInit {
  // Tab Variable
  tabIndex: number = 0;
  tabList: ITabList[];

  status: string | null = null;
  protected readonly TabListLabelDetailSpm = TabListLabelDetailSpm;
  constructor(public utilities: UtilitiesService, private activatedRoute: ActivatedRoute,
              private pageInfoService: PageInfoService, public spmService: SpmService) {
  }

  ngOnInit(): void {
    this.pageInfoService.updateTitle('Detail Surat Perintah Muat');
    this.initPageInfo();
    this.initTabList();
  }

  initPageInfo() {
    this.status = this.activatedRoute.snapshot.params['status'];
  }

  initTabList() {
    this.tabList = [
      {
        tab: 'detail_spm',
        enum: 'TAB_DETAIL_SPM',
        labelKey: 'detail_spm',
        privilege: null,
      },
    ];
    if (this.status?.toUpperCase() === StatusSPM.FINISH || this.status?.toUpperCase() === StatusSPM.PARTIAL_COMPLETED) {
      const tabDO = {
        tab: 'delivery_order',
        enum: 'TAB_DELIVERY_ORDER',
        labelKey: 'delivery_order',
        privilege: null,
      };
      this.tabList.push(tabDO);
    }
  }

  isHaveTab() {
    if (this.spmService.IsViewSpmCompensation()) {
      return false;
    } else {
      return this.status?.toUpperCase() === StatusSPM.FINISH || this.status?.toUpperCase() === StatusSPM.PARTIAL_COMPLETED;
    }
  }

  goToBack = () => history.back();

  handleTabChange = (e: number) => (this.tabIndex = e);
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
