import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ListDeliveryOrderComponent } from './list-delivery-order/list-delivery-order.component';
import { DetailDeliveryOrderComponent } from './detail-delivery-order/detail-delivery-order.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full',
  },
  {
    path: 'list',
    component: ListDeliveryOrderComponent,
    data: {
      enum: 'LIST_DELIVERY_ORDER',
    },
  },
  {
    path: 'detail/:id',
    component: DetailDeliveryOrderComponent,
    data: {
      enum: 'DETAIL_DELIVERY_ORDER',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DeliveryOrderRoutingModule {}
