import { IProgramMarketingDetailLegacy } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.interface';

export interface PurchaseOrder {
  id: string;
  code: string;
  distributor_id: string;
  distributor_name: string;
  total_box_item: number;
  total_qty_item: number;
  expiration_date: string;
  status_enum: string;
  status: string;
}

export interface PurchaseOrderPending extends PurchaseOrder {
  regional_head: string;
  regional_director: string;
}

export interface IInitPurchaseOrderDetail {
  po_code: string;
  po_status: string;
}

export interface PurchaseOrderDetailOld {
  order_items_list?: OrderItemsList[];
  expiration_date: string;
  code: string;
  status_enum: string;
  status: string;
  payment_type_enum: string;
  payment_type: string;
  created_date: string;
  approval_sales: string;
  approval_finance: string;
  approval_regional_director: string;
  approval_regional_head: string;
  approval_date: string;
  approval_date_regional_director: string;
  approval_date_regional_head: string;
  approval_date_finance: string;
  distributor_id: string;
  salesman: string;
  distributor_code: string;
  distributor_name: string;
  distributor_phone_number: string;
  distributor_full_address: string;
  province: string;
  regency: string;
  district: string;
  sub_district: string;
  address_id: string;
  delivery_destination: string;
  pic_name: string;
  pic_phone_number: string;
  address_province: string;
  address_regency: string;
  address_district: string;
  address_sub_district: string;
  delivery_full_address: string;
  items_list_response_list: ItemsListResponseList[];
  qty_order_product: number;
  total_order_box: number;
  total_order_qty: number;
  total_order_weight: string;
  total_weight_with_unit: string;
  sub_total: number;
  discount_cbd: number;
  is_have_cbd_discount: boolean;
  total_discount_cbd: number;
  total_price: number;
  is_distributor_edited: boolean;
  reason_note: IReasonNotePurchaseOrder;
  product_bonus_list: ItemsListResponseList[];
}

export interface PurchaseOrderNeedFulfill extends PurchaseOrderDetailOld {
  id: string;
  expiration_date: string;
  is_distributor_edited: boolean;
  order_items_list: OrderItemsList[];
}

export interface ItemsListResponseList {
  product_id: string;
  product_name: string;
  qty_box: number;
  qty_box_unit: string;
  total_qty_unit: number | null;
  qty_item_unit: string;
  total_weight_unit: string | null;
  sub_total: number;
  discount_sales: number;
  total_discount_sales: number;
  total_price: number;
  image_url: string;
  is_product_bonus?: boolean;
}

export interface OrderItemsList {
  product_id: string;
  product_name: string;
  qty_box: number;
  total_qty_unit: number;
  total_weight_unit: number;
  sub_total: number;
  discount_sales: number;
  total_discount_sales: number;
  total_price: number;
  image_url: string;
  note: string;
}

export interface FilterStatus {
  status_enum: string;
  status_value: string;
}

export interface CounterHeader {
  waiting_sales: number;
  reserved: number;
  outstanding: number;
  on_progress: number;
}

export interface CheckWarehouse {
  is_have_warehouse: boolean;
  assign_warehouse: boolean;
  message: string;
  area_id?: string;
}

export interface IApprovalPurchaseOrder {
  purchase_order_approve_enum: string;
  reason: string;
}

export interface IReasonNotePurchaseOrder {
  title: string;
  suggestion?: string;
  description: string;
}

export interface IApprovalDatePurchaseOrder {
  icon: string;
  label: string;
  approve_by?: string;
  value: string | null;
}

export interface IFilterStatusPO {
  status_enum: string;
  status_enum_string: string;
}

// Program Marketing
export interface IProgramMarketing {
  program_name: string;
  program_type: string;
  reward: string;
  max_budget: number;
  discounts: string[];
  detail: IProgramMarketingDetailLegacy;
}

export interface IProgramMarketingList {
  program_marketing_list: IProgramMarketing[];
}

// New Interface For Detail Purchase Order
export interface IPurchaseOrderDetail {
  code: string;
  status_enum: string;
  status_string: string;
  expired_date: number; // Epoch timestamp
  remaining_days: number;
  total_product: number;
  total_sale_unit: number;
  payment_type_enum: string;
  payment_type: string;
  final_price: number;
  created_changes: IChangeLog;
  approved_regional_head_changes: IChangeLog;
  approved_regional_director_changes: IChangeLog;
  approved_finance_changes: IChangeLog | null; // May be null
  detail_distributor: IDistributorDetails;
  delivery_information: IDeliveryInformation;
  distributor_note_order: string | null; // nullable property
  reason_note: IReasonNotePurchaseOrder;
}

interface IChangeLog {
  actor: string;
  date: number; // Epoch
  document: string | null; // May be null
}

interface IDistributorDetails {
  regional_head: string;
  distributor_code: string;
  distributor_name: string;
  distributor_phone_number: string;
  full_address: string;
}

interface IDeliveryInformation {
  receiver_name: string;
  receiver_number: string;
  label: string;
  full_address: string;
}
