import { Component, OnInit, ViewChild } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { INote } from '@models/product-order.model';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-modal-note-estimation',
  templateUrl: './modal-note-estimation.component.html',
  styleUrls: ['./modal-note-estimation.component.scss'],
})
export class ModalNoteEstimationComponent extends BaseComponent implements OnInit {
  @ViewChild('modalNote') modalNote!: ModalComponent;
  modalConfigNote = <ModalConfig>{
    modalTitle: 'KONFIRMASI PRODUK TIDAK TERSEDIA',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Submit',
    disableDismissButton:() => !this.selectedNote,
    onDismiss: () => this.submitNote(),
  };

  noteTitle!: string;
  selectedNote!: string;
  noteItems!: INote[];

  constructor(private baseService: BaseService) {
    super();
  }

  ngOnInit() {
    this.getListNote();
  }

  getListNote() {
    this.baseService
      .getData<INote[]>(API.GET_LIST_NOTE)
      .pipe(map((resp) => resp && resp.data))
      .subscribe((data) => {
        if (!data) return;
        this.noteItems = data;
      });
  }

  submitNote() {
    // do nothing
    return true;
  }

  openDialog = () => this.modalNote.open();
}
