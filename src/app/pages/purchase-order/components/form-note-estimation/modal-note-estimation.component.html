<app-modal #modalNote [modalConfig]="modalConfigNote">
  <div class="text-left">
    <label class="mb-4">
      Silahkan tambahkan estimasi ketersediaan dari <b>{{ noteTitle }}</b>
    </label>
    <div class="form-check ps-0 ms-n3">
      <mat-radio-group [(ngModel)]="selectedNote" aria-label="Select an option" class="d-flex flex-column w-100">
        <mat-radio-button *ngFor="let item of noteItems" [value]="item.available_enum" class="mb-2">
          {{ item.display }}
        </mat-radio-button>
      </mat-radio-group>
    </div>
  </div>
</app-modal>
