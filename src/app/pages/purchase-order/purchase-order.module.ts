import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { ComponentsModule } from '@shared/components/components.module';
import { PurchaseOrderListComponent } from './purchase-order-list/purchase-order-list.component';
import { PurchaseOrderRoutingModule } from './purchase-order-routing.module';
import { PurchaseOrderNeedFulfillComponent } from './purchase-order-need-fulfill/purchase-order-need-fulfill.component';
import { MatRadioModule } from '@angular/material/radio';
import { MatTabsModule } from '@angular/material/tabs';
import { PurchaseOrderInProgressComponent } from './purchase-order-in-progress/purchase-order-in-progress.component';
import { PurchaseOrderHistoryComponent } from './purchase-order-history/purchase-order-history.component';
import { PurchaseOrderDetailComponent } from './purchase-order-detail/purchase-order-detail.component';
import { ProductComponent } from './purchase-order-detail/product/product.component';
import { SalesOrderComponent } from './purchase-order-detail/sales-order/sales-order.component';
import { DetailComponent } from './purchase-order-detail/detail/detail.component';
import { CardExpiredComponent } from './purchase-order-detail/detail/component/card-expired/card-expired.component';
import { CardInProgressComponent } from './purchase-order-detail/detail/component/card-in-progress/card-in-progress.component';
import { PurchaseOrderWaitingComponent } from './purchase-order-waiting/purchase-order-waiting.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DirectiveModule } from '@directives/directive.module';
import { MatChipsModule } from '@angular/material/chips';
import { ProgramMarketingNoteViewComponent } from './purchase-order-detail/detail/component/program-marketing-note-view/program-marketing-note-view.component';
import { PoComponentModule } from './components/po-component.module';
import { CardInformationDistributorComponent } from './purchase-order-detail/detail/component/card-information-distributor/card-information-distributor.component';
import { CardInformationDeliveryComponent } from './purchase-order-detail/detail/component/card-information-delivery/card-information-delivery.component';
import { CardProgramMarketingListComponent } from './purchase-order-detail/detail/component/card-program-marketing-list/card-program-marketing-list.component';
import { CardHeaderPurchaseOrderComponent } from './purchase-order-detail/detail/component/card-header-purchase-order/card-header-purchase-order.component';
import { ListPurchaseOrderComponent } from './components/list-purchase-order/list-purchase-order.component';

@NgModule({
  declarations: [
    PurchaseOrderListComponent,
    PurchaseOrderNeedFulfillComponent,
    PurchaseOrderInProgressComponent,
    PurchaseOrderHistoryComponent,
    PurchaseOrderDetailComponent,
    ListPurchaseOrderComponent,
    ProductComponent,
    SalesOrderComponent,
    DetailComponent,
    CardExpiredComponent,
    CardInProgressComponent,
    PurchaseOrderWaitingComponent,
    ProgramMarketingNoteViewComponent,
    CardInformationDistributorComponent,
    CardInformationDeliveryComponent,
    CardHeaderPurchaseOrderComponent,
    CardProgramMarketingListComponent,
  ],
  exports: [FormsModule, ReactiveFormsModule, CardProgramMarketingListComponent],
  imports: [
    ComponentsModule,
    PurchaseOrderRoutingModule,
    CommonModule,
    MatTableModule,
    MatSortModule,
    InlineSVGModule,
    ReactiveFormsModule,
    FormsModule,
    MatRadioModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    DirectiveModule,
    MatChipsModule,
    PoComponentModule,
  ],
})
export class PurchaseOrderModule {}
