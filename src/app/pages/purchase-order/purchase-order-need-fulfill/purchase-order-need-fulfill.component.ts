import { Component, OnInit } from '@angular/core';
import { API } from '@config/constants/api.constant';
import { PageLink } from '@metronic/layout';
import { TableColumn } from '@shared/interface/table.interface';
import { UtilitiesService } from '@services/utilities.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { PurchaseOrderService } from '../purchase-order.service';

@Component({
  selector: 'app-purchase-order-need-fulfill',
  templateUrl: './purchase-order-need-fulfill.component.html',
  styleUrls: ['./purchase-order-need-fulfill.component.scss'],
})
export class PurchaseOrderNeedFulfillComponent implements OnInit {
  // set component
  title: string = 'Siap Diproses';
  privilageDetailNeedFulFill: boolean;
  links: Array<PageLink> = [
    {
      title: 'Purchase Order',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: '',
      path: '',
      isActive: true,
      attributes: this.title,
    },
  ];
  apiUrl: string = API.LIST_PURCHASE_ORDER_NEED_FULFILL;
  tableColumns: TableColumn[];
  subMenu: string = 'needfulfill';

  constructor(private utilities: UtilitiesService, private rolePrivilegeService: RolePrivilegeService, private purchaseOrderService: PurchaseOrderService) {}

  ngOnInit(): void {
    this.privilageDetailNeedFulFill = this.rolePrivilegeService.checkPrivilege('PURCHASE_ORDER', 'LIST_PO_NEED_FULLFILL', 'CTA_VIEW_DETAIL');
    this.setTableData();
  }

  setTableData() {
    this.tableColumns = this.utilities.privilegeTableColumns(this.privilageDetailNeedFulFill, this.purchaseOrderService.getPurchaseOrderTableColumns());
  }
}
