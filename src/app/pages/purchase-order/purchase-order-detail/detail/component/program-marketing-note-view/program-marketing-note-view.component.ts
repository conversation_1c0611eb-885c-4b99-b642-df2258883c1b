import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IProgramMarketing, IProgramMarketingList } from '../../../../purchase.order.interface';
import { UtilitiesService } from '@services/utilities.service';
import { BehaviorSubject } from 'rxjs';
import { ModalDetailPromagComponent } from '@shared/components/modal-detail-promag/modal-detail-promag.component';
import { IProgramMarketingDetailLegacy } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.interface';
import { EnumProgramMarketingDiscountCategory, EnumProgramMarketingType } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { ProgramMarketingLegacyService } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.service';

@Component({
  selector: 'app-program-marketing-note-view',
  templateUrl: './program-marketing-note-view.component.html',
  styleUrls: ['./program-marketing-note-view.component.scss'],
})
export class ProgramMarketingNoteViewComponent implements OnInit {
  @Input() data!: IProgramMarketingList;

  STRING_CONSTANTS = STRING_CONSTANTS;
  detail: BehaviorSubject<IProgramMarketingDetailLegacy> = new BehaviorSubject<IProgramMarketingDetailLegacy>({} as IProgramMarketingDetailLegacy);

  @ViewChild('modalDetail') private modalDetail: ModalDetailPromagComponent;

  constructor(public utils: UtilitiesService, private programMarketingService: ProgramMarketingLegacyService) {}

  ngOnInit() {}

  openModal(value: IProgramMarketing, title: string) {
    return this.modalDetail.openModal(value.detail, title);
  }

  getProgramMarketingType(detail: IProgramMarketingDetailLegacy) {
    return !this.utils.isObjectEmpty(detail) ? detail.information.program_type_enum : this.programMarketingService.ProgramMarketingType;
  }

  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumProgramMarketingDiscountCategory = EnumProgramMarketingDiscountCategory;
}
