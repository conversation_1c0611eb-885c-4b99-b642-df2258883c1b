<ng-template #pageTpl>
  <app-mai-tab-group (tabChange)="handleTabChange($event)" [selectedTabIndex]="defaultTabIndex" [tabGroupClass]="'box'">
    <ng-container *ngFor="let tc of tabComponents">
      <app-mai-tab *ngIf="isAllowAccessTab(tc)" [component]="tc.component" [disabled]="tc.disabled" [labelEnum]="tc.enum" [title]="tc.title" />
    </ng-container>
  </app-mai-tab-group>
</ng-template>

<ng-container *ngIf="isLoading | async; else pageTpl">
  <div class="d-flex align-items-center justify-content-center mh-250px my-8">
    <mat-spinner></mat-spinner>
  </div>
</ng-container>
