<!-- Card Header -->
<app-fullscreen-loader *ngIf="isLoading | async" />
<app-card-header-spm [headerConfig]="{ useStatusBadge: false, useSection: true }" [isLoading]="isLoading" [title]="so_code" />

<!-- Card: Product order -->
<app-card-product-order #cardProductOrder (eventFromChild)="handleEmitter($event)" [tableData]="SpmProductOrderComponent" />

<!-- Card: Product program marketing -->
<app-card-product-promag #cardProductPromag [showView]="(soService.initHeaderCreateSpm$ | async)?.is_have_program_marketing ?? false" />

<ng-container *ngIf="cardSectionsIsLoaded()">
  <!-- Section: CTA -->
  <fieldset>
    <div class="d-flex justify-content-between align-items-center mt-6">
      <button (click)="modalCancel.openDialog()" class="btn btn-outline btn-outline-gray">
        <span class="text-primary">Batal</span>
      </button>
      <button (click)="modalConfirmCreateSPM.open()" [disabled]="disabledSubmitState()" class="btn btn-primary ms-4">
        <span>Buat SPM</span>
      </button>
    </div>
  </fieldset>

  <!-- Modals -->
  <app-modal-static-cancel #modalCancel [config]="modalConfigStaticCancel" [text]="getCancelText()" />

  <app-modal #modalConfirmCreateSPM [modalConfig]="modalConfigConfirmSPM" [modalOptions]="modalGeneralOpt">
    <div *ngIf="selectedGudangIsMostRequest()" class="mb-6">
      <app-note-view
        [color]="'info'"
        [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
        [text]="'Surat Perintah Muat akan diteruskan kepada Admin Pusat untuk proses approval.'"
      ></app-note-view>
    </div>

    <div class="text-left">
      <div class="mb-6"><span>Apakah yakin melanjutkan proses pembuatan Surat Perintah Muat dengan detail:</span></div>
      <!-- Produk Dipenuhi -->
      <div *ngIf="hasProductOrder(payloadSubject.value)" class="mb-6 border-bottom border-gray-300 pb-6">
        <p class="label">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_BOX_PRODUCT" class="me-2"></span>
          <span class="fw-bold">Produk Dipenuhi</span>
        </p>
        <ul *ngFor="let item of getFulFilledProducts(); let i = index" class="mb-0">
          <li>
            <span>{{ item.product_name }} ({{ item.qty_fulfilled }} {{ getProductSaleUnit(item.product_id) }})</span>
          </li>
        </ul>
      </div>

      <!-- Produk Hadiah -->
      <div *ngIf="!!cardProductPromag.dataValue.length" class="mb-6 border-bottom border-gray-300 pb-6">
        <p class="label">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_BOX_REWARD" class="me-2"></span>
          <span class="fw-bold">Produk Hadiah</span>
        </p>
        <ul *ngFor="let item of getBonusProducts(); let i = index">
          <li>
            <span>{{ item.product_name }} ({{ item.qty_outstanding }} {{ item.sale_unit }})</span>
          </li>
        </ul>
      </div>

      <!-- Produk Belum Dipenuhi -->
      <ng-container *ngIf="hasProductOrder(payloadSubject.value)">
        <div *ngIf="hasUnmetProducts()" class="mb-6 border-bottom border-gray-300 pb-6">
          <p class="label">
            <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_BOX_PRODUCT_UNMET" class="me-2"></span>
            <span class="fw-bold">Produk Belum Dipenuhi</span>
          </p>
          <ul *ngFor="let item of getUnmetProducts(); let i = index">
            <li>
              <span>{{ item.product_name }} ({{ item.qty_less }} {{ getProductSaleUnit(item.product_id) }})</span>
            </li>
          </ul>
        </div>
      </ng-container>

      <!-- Gudang Pengirim -->
      <ng-container *ngIf="warehouse | async as gudang">
        <div class="mb-6">
          <p class="label">
            <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_DELIVERY_TRUCK" class="me-2"></span>
            <span class="fw-bold">Gudang Pengirim</span>
          </p>
          <span>{{ gudang.name }}</span>
          <span class="d-block">{{ gudang.address }}</span>
        </div>
      </ng-container>
    </div>
  </app-modal>

  <app-modal #modalConfirmContinueSO [modalConfig]="modalConfigContinueSO" [modalOptions]="modalGeneralOpt">
    <div class="text-left mb-n8">
      <p class="mb-2">Terdapat produk yang belum dipenuhi. Apakah anda ingin melanjutkan proses Sales Order?</p>
      <div class="card-product-not-available mb-4">
        <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_INFO" class="sv-icon svg-icon-2"></span>
        <div class="text ms-auto">
          <span>Jika memilih Selesaikan Sales Order:</span>
          <ul>
            <li>Tidak dapat membuat SPM dari Produk Menunggu</li>
            <li>Produk tidak diproses dikembalikan ke Purchase Order</li>
          </ul>
        </div>
      </div>
    </div>
  </app-modal>

  <app-modal #modalResponse [modalConfig]="modalConfigResponse">
    <div class="d-flex flex-column justify-content-center align-items-center">
      <ng-container *ngIf="isLoading | async; else responseMessageTpl">
        <div class="mt-8">
          <mat-spinner></mat-spinner>
        </div>
      </ng-container>
      <ng-template #responseMessageTpl>
        <div class="mt-8"><span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span></div>
        <div>{{ dataResponse.value }}</div>
      </ng-template>
    </div>
  </app-modal>
</ng-container>
