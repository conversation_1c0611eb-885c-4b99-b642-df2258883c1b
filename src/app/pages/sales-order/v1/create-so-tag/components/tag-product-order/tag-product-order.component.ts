import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { TableColumn } from '@shared/interface/table.interface';
import { BehaviorSubject } from 'rxjs';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';
import { IProductOrder__ProductList } from '@shared/interface/product-order.interface';
import { IFormCreateSO__ProductField, IFormCreateSOTag__ProductField, IInitCreateSO__ProductOrder, IPayloadProductQty } from '@models/product-order.model';
import { ICSourceData } from '@shared/components/form/input-counter-order/input-counter-order.interface';
import { SalesOrderTagService } from '../../../../sales-order-tag.service';

@Component({
  selector: 'app-tag-product-order.component',
  templateUrl: './tag-product-order.component.html',
  styleUrls: ['./tag-product-order.component.scss'],
})
export class TagProductOrderComponent extends BaseComponent implements OnInit {
  id!: string;

  tableColumns!: TableColumn[];
  displayedColumns!: string[];

  productOrderSubject = new BehaviorSubject(this.soTagService.ProductOrderList);
  isFinishLoadingSubject = new BehaviorSubject(true);
  payloadProductOrder$ = new BehaviorSubject({} as IPayloadProductQty);

  formProduct: FormGroup = this.fb.group({
    products: this.fb.array([]),
  });

  get FormProductOrder() {
    return <FormGroup>this.formProduct;
  }

  get FormProductsArray() {
    return <FormArray>this.FormProductOrder.get('products');
  }

  getProductFormControl(idx: number) {
    return <FormControl>this.FormProductsArray.at(idx);
  }

  getValueQtyLess(idx: number) {
    return this.getProductFormControl(idx).value.qty_less;
  }

  getValueQtyOrder(idx: number) {
    return this.getProductFormControl(idx).value.qty_order;
  }

  constructor(private activeRoute: ActivatedRoute, private fb: FormBuilder, private soTagService: SalesOrderTagService, private utils: UtilitiesService) {
    super();
    this.activeRoute.queryParams.subscribe((value) => {
      this.id = value.so_id;
    });
  }

  ngOnInit() {
    this.setTableData();
    this.getProductOrder();
  }

  setTableData() {
    this.tableColumns = this.soTagService.tableProductCreateSOTag;
    this.displayedColumns = this.tableColumns.map((head) => head.key);
  }

  getProductOrder() {
    this.isFinishLoadingSubject.next(false);
    this.soTagService.getListProductOrderSO<IProductOrder__ProductList[]>(this.id).subscribe((data) => {
      if (!data) return;
      this.productOrderSubject.next(data);
      this.initForm();
      this.isFinishLoadingSubject.next(true);
    });
  }

  initForm() {
    this.productOrderSubject.value.map((item) => this.FormProductsArray.push(this.createProductField(item)));
    this.handleQtyChanges();
  }

  createProductField(data: IInitCreateSO__ProductOrder) {
    const { product_id, product_image, product_name, qty_outstanding } = data;
    return this.fb.group<IFormCreateSOTag__ProductField>({
      product_id,
      product_name,
      product_image,
      qty_order: qty_outstanding,
      qty_fulfilled: qty_outstanding,
      qty_less: 0,
      packaging_type: data.sale_unit,
      note: '',
    });
  }

  handleNoImage = (e: Event) => this.utils.setDefaultImageProduct(e);

  handleQtyChanges() {
    this.FormProductsArray.controls.map((ctrl, index) => {
      ctrl.get('qty_fulfilled')?.valueChanges.subscribe((val) => {
        const qtyOrder = this.getValueQtyOrder(index);
        ctrl.get('qty_less')?.patchValue(qtyOrder - val);
        if (val === qtyOrder) ctrl.get('note')?.patchValue(null);

        // payload for parent - Create SO
        const payload: IPayloadProductQty = { products: [] };
        this.formProduct.value.products.map((item: IFormCreateSO__ProductField) => {
          payload.products.push({ product_id: item.product_id, qty: item.qty_fulfilled });
        });
        this.payloadProductOrder$.next(payload);
      });
    });
  }

  get orderListAsSourceData() {
    return this.productOrderSubject.value.map((product) => this.mapToSourceDataItem(product));
  }

  mapToSourceDataItem(item: IInitCreateSO__ProductOrder) {
    const { qty_outstanding, available_stock } = item;
    return <ICSourceData>{ order_item: qty_outstanding, available_stock };
  }
}
