import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { SalesOrderService } from '../../../v1/sales-order.service';
import { TableColumn } from '@shared/interface/table.interface';
import { BehaviorSubject, Subscription } from 'rxjs';
import { productPromagListTableColumns } from '../../../sales-order.data';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ModalDetailPromagComponent } from '@shared/components/modal-detail-promag/modal-detail-promag.component';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { IProgramMarketingDetailLegacy } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.interface';

@Component({
  selector: 'app-table-product-promag-list',
  templateUrl: './table-product-promag-list.component.html',
  styleUrls: ['./table-product-promag-list.component.scss'],
})
export class TableProductPromagListComponent implements OnInit, OnDestroy {
  @Input() title = 'Produk Program Marketing';

  id!: string;

  // table
  tableColumns!: TableColumn[];
  displayedColumns!: string[];
  tableFooter: string[];

  productListSubject = new BehaviorSubject(this.soService.ProductPromagList);
  productNotFullFill: string[] = [];
  isFinishLoadingSubject = new BehaviorSubject(true);

  @ViewChild('modalDetailPromag') modalDetailPromag!: ModalDetailPromagComponent;

  private unsubscribe = <Subscription[]>[];

  constructor(private soService: SalesOrderService, private utils: UtilitiesService, private cdr: ChangeDetectorRef, private baseService: BaseService) {}

  @ViewChild('modalDetailNeedFullFill') private modalDetailNeedFullFill: ModalComponent;
  modalDetailNeedFullFillConfig: ModalConfig = {
    modalTitle: 'Produk Tidak Diproses',
    showFooter: false,
    showHeader: false,
    hideCloseButton: () => true,
  };

  ngOnInit() {
    this.setTableData();
    this.getProductList();
  }

  setTableData() {
    this.tableColumns = productPromagListTableColumns;
    this.tableFooter = ['total_text', 'total_sale_unit', 'total_delivery_unit', 'total_process_sale_unit'];
    this.displayedColumns = this.tableColumns.map((head) => head.key);
  }

  getProductList() {
    this.isFinishLoadingSubject.next(false);
    this.soService.getProductListProgramMarketing().subscribe((data) => {
      if (!data) return;
      this.productListSubject.next(data);
      this.isFinishLoadingSubject.next(true);
      this.cdr.detectChanges();
    });
  }

  handleDetail(id: string, name: string) {
    this.baseService.getData<IProgramMarketingDetailLegacy>(API.PROGRAM_MARKETING.DETAIL_MODAL_PROGRAM + id).subscribe((resp) => {
      if (resp && resp.success) {
        return this.modalDetailPromag.openModal(resp.data, name);
      }
    });
  }

  handleDetailNeedFullFill(value: string[]) {
    this.productNotFullFill = value;
    this.cdr.detectChanges();
    this.modalDetailNeedFullFill.open();
  }

  handleNoImage = (e: Event) => this.utils.setDefaultImageProduct(e);

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
