<!-- <pre>show empty {{ showEmptyState() }}</pre>
<pre>has products:{{hasProducts()}}</pre>
<pre>card is loaded: {{cardIsLoaded() }}</pre>
<pre>isLoading: {{isLoading|async}}</pre> -->
<!-- <pre>get exp date {{ getExpirationDate() }}</pre> -->

<app-card-empty *ngIf="!isLoading.value && !hasProducts()" icon="{{ iconNone }}" text="Tidak ada data produk unscan." />

<!--  note view expired -->
<app-note-view
  *ngIf="isExpired"
  [color]="'warning-secondary'"
  [icon]="STRING_CONSTANTS.ICON.IC_DANGER_NONE"
  [text]="'Sales Order telah kedaluwarsa pada tanggal ' + formatExpDate() + '.'"
/>

<!--  note view before expired -->
<app-note-view
  *ngIf="showInfoExpiration()"
  [color]="'info'"
  [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
  [text]="'Silahkan buat Surat Perintah Muat sebelum Sales Order kedaluwarsa pada ' + formatExpDate() + '.'"
/>

<app-card-product-need-full-fill #cardProductNeedFullFill [isSalesOrder]="true" [tableColumns]="tableColumns" [url]="API.SALES_ORDER.GET_DETAIL_LIST_PRODUCT_NEEDFULFILL + id" />

<div class="button gap-5">
  <button (click)="!isLoading.value && modalCloseSO.open()" *ngIf="CTACloseSO" class="btn btn-outline text-primary" style="width: 184px" type="button">
    <span *ngIf="!isLoading.value">Selesaikan SO</span>
    <div *ngIf="isLoading.value" class="d-flex justify-content-center align-items-center">
      <mat-spinner style="height: 20px"></mat-spinner>
    </div>
  </button>
  <button (click)="createSPM()" *ngIf="CTACreateSPM" class="btn btn-primary" style="width: 184px" type="button">Buat SPM</button>
</div>

<app-modal-close-so #modalCloseSO [id]="id" [isLoading]="isLoading" (isLoadingSubmit)="isLoading.next($event)"></app-modal-close-so>
