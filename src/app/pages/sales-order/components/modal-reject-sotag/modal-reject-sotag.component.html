<app-modal #modalRejectDialog [modalConfig]="modalConfig">
  <div class="mb-n8">
    <form [formGroup]="formReason">
      <app-input-text
        [textArea]="true"
        [label]="'Silahkan input alasan penolakan Sales Order'"
        [placeholder]="'Input alasan penolakan'"
        [formControlName]="'reject_reason'"
        [columnStacked]="true"
        ngDefaultControl
      ></app-input-text>
    </form>
  </div>

  <!--  <p>form approval value: {{ formApproval.value | json }}</p>-->
</app-modal>

<app-modal #modalResponseDialog [modalConfig]="modalConfigResponse">
  <div class="d-flex flex-column justify-content-center align-items-center mb-n6">
    <ng-container *ngIf="isLoading | async; else responseTpl">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>
    <ng-template #responseTpl>
      <span [inlineSVG]="STRING_CONSTANTS.ICON.ERROR_ALERT"></span>
      <p class="my-4">{{ responsePost.value }}</p>
      <app-note-view
        [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
        [classNoteView]="'text-left'"
        [color]="'info'"
        [text]="'Produk yang ditolak dikembalikan ke Purchase Order.'"
      ></app-note-view>
    </ng-template>
  </div>
</app-modal>
