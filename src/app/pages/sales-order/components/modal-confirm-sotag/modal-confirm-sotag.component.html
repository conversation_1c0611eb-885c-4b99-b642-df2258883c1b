<!-- CONFIRM APPROVE MODAL -->
<app-modal #modalConfirmDialog [modalConfig]="modalConfigConfirm" [modalOptions]="{ size: 'md' }">
  <div class="text-left mb-n8">
    <p class="mb-2">Apakah yakin menyetujui Sales Order untuk produk:</p>
    <ul>
      <ng-container *ngFor="let item of orderProductList">
        <li *ngIf="item.qty_fulfilled && item.qty_fulfilled > 0">
          <span>{{ item.product_name }} ({{ item.qty_fulfilled }} {{ item.packaging_type }})</span>
        </li>
      </ng-container>
    </ul>

    <!-- produk tidak dipenuhi -->
    <ng-container *ngIf="getUnmetProduct()?.length">
      <app-note-view [color]="'secondary'" [extraContent]="true" [icon]="STRING_CONSTANTS.ICON.IC_INFO">
        <p class="mb-2">Produk tidak dipenuhi:</p>
        <ul>
          <li *ngFor="let item of getUnmetProduct() as products">
            <span>{{ item['product_name'] }} ({{ getUnmetProductQty(item) }} {{ item.packaging_type }})</span>
          </li>
        </ul>
      </app-note-view>
    </ng-container>
  </div>
</app-modal>

<!-- PRODUCT NOT MET REASON MODAL -->
<app-modal #modalReasonDialog [modalConfig]="modalConfigReason" [modalOptions]="{ size: 'md' }">
  <div class="mb-n8">
    <p class="mb-2">Silahkan masukkan alasan produk tidak dipenuhi:</p>

    <ul>
      <li *ngFor="let item of getUnmetProduct() as products">
        <span>{{ item.product_name }} ({{ getUnmetProductQty(item) }} {{ item.packaging_type }})</span>
      </li>
    </ul>

    <form [formGroup]="reasonForm">
      <app-input-text
        [textArea]="true"
        [required]="true"
        [placeholder]="'Silahkan input alasan'"
        [formControlName]="'reason'"
        [columnStacked]="true"
        ngDefaultControl
      ></app-input-text>
    </form>
  </div>
</app-modal>

<!-- RESPONSE MODAL -->
<app-modal #modalResponseDialog [modalConfig]="modalConfigResponse">
  <div class="d-flex flex-column justify-content-center align-items-center">
    <ng-container *ngIf="loadingPost | async; else responseMessageTpl">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>
    <ng-template #responseMessageTpl>
      <div class="mt-8">
        <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      </div>
      <div class="mb-5">
        {{ responsePost.value }}
      </div>
      <app-note-view
        *ngIf="checkUnmetProduct()"
        [classNoteView]="'mb-0'"
        [color]="'info'"
        [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
        [text]="'Semua produk yang gagal dimuat dikembalikan ke Purchase Order.'"
      >
      </app-note-view>
    </ng-template>
  </div>
</app-modal>
