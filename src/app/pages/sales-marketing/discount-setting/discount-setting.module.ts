import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SalesMarketingRoutingModule } from '../sales-marketing-routing.module';
import { ComponentsModule } from '@shared/components/components.module';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { SalesDiscountComponent } from './sales-discount/sales-discount.component';
import { SwiperModule } from 'swiper/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { CbdDiscountComponent } from './cbd-discount/cbd-discount.component';
import { CbdHistoryComponent } from './cbd-discount/cbd-history/cbd-history.component';
import { SalesHistoryComponent } from './sales-discount/sales-history/sales-history.component';
import { SalesFormSettingsComponent } from './sales-discount/sales-form-settings/sales-form-settings.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ProductsModule } from '../../products/products.module';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { DaterangeDiscountPeriodComponent } from './sales-discount/sales-form-settings/daterange-discount-period/daterange-discount-period.component';
import { DirectiveModule } from '@directives/directive.module';

@NgModule({
  declarations: [SalesDiscountComponent, CbdDiscountComponent, CbdHistoryComponent, SalesHistoryComponent, SalesFormSettingsComponent, DaterangeDiscountPeriodComponent],
  imports: [
    CommonModule,
    MatTableModule,
    MatSortModule,
    SalesMarketingRoutingModule,
    ComponentsModule,
    MatProgressSpinnerModule,
    InlineSVGModule,
    SwiperModule,
    FormsModule,
    NgbPopoverModule,
    ReactiveFormsModule,
    MatTooltipModule,
    ProductsModule,
    MatInputModule,
    MatDatepickerModule,
    MatIconModule,
    MatRadioModule,
    DirectiveModule,
  ],
  exports: [],
})
export class DiscountSettingModule {}
