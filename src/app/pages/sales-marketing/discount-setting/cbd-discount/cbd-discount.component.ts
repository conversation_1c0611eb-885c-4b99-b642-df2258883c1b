import { Component, <PERSON><PERSON><PERSON><PERSON>, Input, On<PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { SwiperComponent } from 'swiper/angular';
import { BehaviorSubject, Subscription } from 'rxjs';
import { IListCbdDiscount, IListCbdHistory } from '../discount-setting.interface';
import { TableColumn } from '@shared/interface/table.interface';
import { PageInfoService, PageLink } from '@metronic/layout';
import { BaseDatasource } from '@shared/base/base.datasource';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ActivatedRoute, Router } from '@angular/router';
import { UrlUtilsService } from '@utils/url-utils.service';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { UtilitiesService } from '@services/utilities.service';
import { API } from '@config/constants/api.constant';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-cbd-discount',
  templateUrl: './cbd-discount.component.html',
  styleUrls: ['./cbd-discount.component.scss'],
})
export class CbdDiscountComponent implements OnInit, OnDestroy, DoCheck {
  @ViewChild('swiperCBD', { static: false }) swiperCBD: SwiperComponent;
  @Input() Data: BehaviorSubject<IListCbdDiscount | undefined>;
  @Input() distributorInfo: any;
  modalConfigEdit: ModalConfig = {
    modalTitle: 'EDIT CBD DISCOUNT',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Lanjutkan',
    showFooter: true,
    disableDismissButton: () => false,
  };
  tableColumns: TableColumn[];
  links: Array<PageLink>;
  displayedColumns: string[];
  string_filter: string = '';
  dataDetail: IListCbdHistory;
  reason: string = '';
  baseDatasource: BaseDatasource<IListCbdDiscount>;
  id: BehaviorSubject<string | null> = new BehaviorSubject<string | null>('');

  productId: BehaviorSubject<string | null> = new BehaviorSubject<string | null>('');
  urlPreview: BehaviorSubject<string> = new BehaviorSubject<string>('');
  imageList: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  iconClock: string = STRING_CONSTANTS.ICON.IC_CLOCK;
  iconNone: string = STRING_CONSTANTS.ILLUSTRATIONS.IL_PERCENT_NONE;
  iconInfo: string = STRING_CONSTANTS.ICON.IC_INFO;
  iconAction: string = STRING_CONSTANTS.ICON.IC_ACTIONS_TABLE;
  iconEdit: string = STRING_CONSTANTS.ICON.IC_EDIT;
  modalDetailConfig: ModalConfig = {
    showFooter: false,
    showHeader: false,
  };

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  @ViewChild('modalEdit') private modalEdit: ModalComponent;
  discountSettingPath = '/sales-marketing/discount-setting';

  private unsubscribe: Subscription[] = [];

  constructor(
    private urlParamService: UrlUtilsService,
    private baseTableService: BaseTableService<any>,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private pageInfoService: PageInfoService,
    public utilities: UtilitiesService,
  ) {
  }

  ngOnInit(): void {
    this.initPageInfo();
    this.setTableData();
    this.queryHandler();
  }

  ngDoCheck() {
    if (this.reason) {
      this.modalConfigEdit = {
        modalTitle: 'EDIT CBD DISCOUNT',
        closeButtonLabel: 'Cancel',
        dismissButtonLabel: 'Lanjutkan',
        showFooter: true,
        disableDismissButton: () => false,
      };
    } else {
      this.modalConfigEdit = {
        modalTitle: 'EDIT CBD DISCOUNT',
        closeButtonLabel: 'Cancel',
        dismissButtonLabel: 'Lanjutkan',
        showFooter: true,
        disableDismissButton: () => true,
      };
    }
  }

  initPageInfo() {
    this.links = [
      {
        title: 'Sales & Marketing',
        path: '',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: 'Discount Setting',
        path: '',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: 'CBD Discount',
        path: '',
        isActive: true,
      },
    ];

    this.pageInfoService.updateBreadcrumbs(this.links);
    this.pageInfoService.updateTitle('CBD Discount', undefined, true);
  }

  setTableData() {
    this.baseTableService.responseDatabase.subscribe((resp) => (this.baseDatasource = resp));

    this.tableColumns = [
      { title: 'DISCOUNT', key: 'max_discount' },
      { title: 'SUBMITTED BY', key: 'submitted_by' },
      { title: 'SUBMITTED TIME', key: 'submitted_time' },
      { title: 'DOCUMENT', key: 'document' },
      { title: 'ACTION', key: 'actions' },
    ];

    this.displayedColumns = this.tableColumns.map((head) => head.key);
  }

  queryHandler() {
    const paramSubs = this.activatedRoute.queryParams.subscribe((params) => {
      const { string_filter } = params;
      this.string_filter = string_filter;
      const param = this.urlParamService.sliceQueryParams();
      this.baseTableService.loadDataTable(API.LIST_CBD_DISCOUNT, param ? param : '');
    });

    this.unsubscribe.push(paramSubs);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  handleGoTo(isEdit = false) {
    return this.router.navigate([this.discountSettingPath + '/cbd-form-settings/form'], {
      queryParams: {
        mode: isEdit ? 'edit' : 'add',
      },
    });
  }

  async handleHistory() {
    return this.router.navigate([this.discountSettingPath + '/cbd/history']).then();
  }

  handleModalEdit(data: any) {
    this.id.next(data.id);
    this.dataDetail = data;
    this.modalEdit.open().then();
  }

  submitForm = () => {
    const _isEdit = true;
    const data = this.dataDetail;
    data.reason = this.reason;

    if (this.dataDetail) {
      this.router
        .navigate([this.discountSettingPath + '/cbd-form-settings/form'], {
          state: data,
          queryParams: {
            mode: _isEdit ? 'edit' : 'add',
            id: this.id.value,
          },
        })
        .then();
    }
    this.modalEdit.close().then();
  };
}

// Todo: Remove unused
