<app-card-header-counter [data]="counterHeader" />
<ng-container [ngTemplateOutlet]="isEmptyData() ? emptyStateTpl : programListTpl"></ng-container>

<ng-template #programListTpl>
  <app-card [cardBodyClasses]="'pt-2'" [header]="true">
    Filter
    <ng-container cardHeader>
      <app-filter-list-program
        [searchInputValue]="search"
        [filterInputActivated]="isActiveFilter"
        [finishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject"
        (actionAddProgram)="handleSelectedProgram($event)"
      />
    </ng-container>

    <ng-container cardBody>
      <!-- search not found -->
      <ng-container *ngIf="searchNotFound(); else tableListTpl">
        <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_PROMO" text="Program Marketing tidak ditemukan." />
      </ng-container>

      <!-- Table Data List -->
      <ng-template #tableListTpl>
        <div class="table-responsive">
          <table mat-table class="table w-100 gy-5 table-row-bordered align-middle" [dataSource]="baseDataSourceList">
            <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
              <th *matHeaderCellDef [ngClass]="renderThClass(tableColumn.key)" class="px-3" mat-header-cell>
                {{ tableColumn.title }}
              </th>

              <td *matCellDef="let element" class="px-3"
                  [ngClass]="{ 'mw-100px': tableColumn.key === 'period', 'mw-200px': tableColumn.key === 'scope' }">
                <ng-container [ngSwitch]="tableColumn.key">
                  <div *ngSwitchCase="'scope'">
                    <app-table-content [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject">
                      <div class="block-ellipsis-customs">{{ utils.arrayStringJoin(element[tableColumn.key], '') }}
                      </div>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'period'">
                    <app-table-content [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject">
                      <div>
                        {{ hasPeriodRange(element) ? element['period_start'] + ' s/d ' + element['period_end'] : '-' }}
                      </div>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'status'">
                    <app-table-content [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject">
                      <span class="badge badge__status {{ 'badge__status--' + element['status_enum'] }}">
                        {{ element['status_string'] }}
                      </span>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'created_date'">
                    <app-table-content [count]="2" [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject"
                                       [type]="'text'">
                      <div>{{ utils.formatEpochToDate(element['created_date']) }}</div>
                      <div>{{ utils.formatEpochToDate(element['created_date'], 'HH:mm:ss') }}</div>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'actions'" class="d-flex align-items-center justify-content-center">
                    <app-table-content [type]="'icon'"
                                       [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject">
                      <span (click)="goToDetail(element['id'])" [inlineSVG]="'./assets/media/icons/ic_task.svg'"
                            class="svg-icon svg-icon-2 me-2 cursor-pointer"></span>
                    </app-table-content>
                  </div>

                  <div *ngSwitchDefault>
                    <app-table-content [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject">
                      <span>{{ element[tableColumn.key] ?? '-' }}</span>
                    </app-table-content>
                  </div>
                </ng-container>
              </td>
            </ng-container>

            <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>

          <div class="d-flex justify-content-between py-4">
            <app-mai-material-bottom-table
              (changePage)="changePageEvent($event)"
              [baseDataTableComponent]="baseDataSourceList"
              [isFinishLoadingSubject]="baseDataSourceList.isFinishLoadingSubject"
              class="w-100"
            ></app-mai-material-bottom-table>
          </div>
        </div>
      </ng-template>
    </ng-container>
  </app-card>
</ng-template>

<ng-template #emptyStateTpl>
  <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_PROMO" text="Belum terdapat Program Marketing.">
    <div *ngIf="!authService.isRoleFinance" class="mt-10">
      <app-button-add-program (selectedProgramType)="handleSelectedProgram($event)" />
    </div>
  </app-card-empty>
</ng-template>
