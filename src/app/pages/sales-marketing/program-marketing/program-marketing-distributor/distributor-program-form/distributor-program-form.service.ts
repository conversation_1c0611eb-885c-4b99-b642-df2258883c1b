import { Injectable } from '@angular/core';
import { BehaviorSubject, shareReplay } from 'rxjs';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { InputSelectMaterialInterface } from '@shared/components/form/input-select-material/input-select-material.interface';
import {
  EnumProgramMarketingDiscountCategory,
  EnumProgramMarketingDiscountProgramType,
  EnumProgramMarketingDiscountProgramTypeString,
  EnumProgramMarketingScope,
} from '../../../program-marketing-legacy/program-marketing.enum';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { IPromagFormPayload__DiscountTerm, IResponseProgramMarketing, ISalesDiscountProgramMarketing } from '../../../program-marketing-legacy/program-marketing-legacy.interface';
import { UtilitiesService } from '@services/utilities.service';

@Injectable({
  providedIn: 'root',
})
export class DistributorProgramFormService {
  private programTermFormSubject = new BehaviorSubject<FormGroup | null>(null);

  private orderTermFormSubject = new BehaviorSubject<FormGroup | null>(null);
  orderTermForm$ = this.orderTermFormSubject.asObservable();

  private discountTermFormSubject = new BehaviorSubject<FormGroup | null>(null);
  discountTermForm$ = this.discountTermFormSubject.asObservable();
  discountTermFormPayload = new BehaviorSubject<IPromagFormPayload__DiscountTerm | null>(null);

  salesDiscount: ISalesDiscountProgramMarketing;

  // discount term form payload
  get DiscountTermFormPayload() {
    return this.discountTermFormPayload.value;
  }

  set DiscountTermFormPayload(value: IPromagFormPayload__DiscountTerm | null) {
    this.discountTermFormPayload.next(value);
  }

  // static data helper
  get OptionsDiscountProgramType() {
    return <InputSelectMaterialInterface[]>[
      {
        value: EnumProgramMarketingDiscountProgramType.EQUAL_SALES_DISCOUNT,
        label: EnumProgramMarketingDiscountProgramTypeString.EQUAL_SALES_DISCOUNT,
        labelExtra: '<span class="text-gray-700 d-block">Mengikuti aturan sales discount yang telah ditentukan oleh Finance Area masing-masing.</span>',
      },
      {
        value: EnumProgramMarketingDiscountProgramType.SET_MAXIMUM_SALES_DISCOUNT,
        label: EnumProgramMarketingDiscountProgramTypeString.SET_MAXIMUM_SALES_DISCOUNT,
        labelExtra: '<span class="text-gray-700 d-block">Maksimal Diskon diatur berdasarkan surat pengajuan Program Marketing.</span>',
      },
      {
        value: EnumProgramMarketingDiscountProgramType.FIXED_SALES_DISCOUNT,
        label: EnumProgramMarketingDiscountProgramTypeString.FIXED_SALES_DISCOUNT,
        labelExtra: '<span class="text-gray-700 d-block">Diskon bersifat tetap dan diatur berdasarkan surat pengajuan Program Marketing.</span>',
      },
      {
        value: EnumProgramMarketingDiscountProgramType.NO_SALES_DISCOUNT,
        label: EnumProgramMarketingDiscountProgramTypeString.NO_SALES_DISCOUNT,
        labelExtra: '<span class="text-gray-700 d-block">Hanya berlaku untuk diskon 0% atau tanpa diskon.</span>',
      },
    ];
  }

  get ExcludedTypeMaxDiscount() {
    // discount program type without input for max discount
    return [EnumProgramMarketingDiscountProgramType.NO_SALES_DISCOUNT, EnumProgramMarketingDiscountProgramType.EQUAL_SALES_DISCOUNT];
  }

  get DiscountDigitsPattern() {
    return /^([1-9]\d*|0)(,(\d{1}|\d{2}))?$/;
  }

  get MaxDiscountPercentageValidators() {
    return [this.maxValueDecimalValidator(99.99), Validators.pattern(this.DiscountDigitsPattern)];
  }

  constructor(private baseService: BaseService, private utils: UtilitiesService) {}

  // Program term form
  updateProgramTermForm(form: FormGroup) {
    this.programTermFormSubject.next(form);
  }

  // Order term form
  updateOrderTermForm(form: FormGroup) {
    this.orderTermFormSubject.next(form);
  }

  // Discount term form
  updateDiscountTermForm(form: FormGroup) {
    this.discountTermFormSubject.next(form);
    this.discountTermForm$ = this.discountTermFormSubject.asObservable();
  }

  postTypeOneShoot(payload: any, id?: string) {
    if (id) {
      return this.baseService.putData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.EDIT.ONE_SHOOT + id, payload).pipe(shareReplay());
    } else {
      return this.baseService.postData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.CREATE.ONE_SHOOT, payload).pipe(shareReplay());
    }
  }

  postTypeProductDiscount(payload: any, id?: string) {
    if (id) {
      return this.baseService.putData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.EDIT.PRODUCT_DISCOUNT + id, payload).pipe(shareReplay());
    } else {
      return this.baseService.postData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.CREATE.PRODUCT_DISCOUNT, payload).pipe(shareReplay());
    }
  }

  postTypePurchaseDiscount(payload: any, id?: string) {
    if (id) {
      return this.baseService.putData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.EDIT.DISCOUNT_PURCHASE + id, payload).pipe(shareReplay());
    } else {
      return this.baseService.postData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.CREATE.DISCOUNT_PURCHASE, payload).pipe(shareReplay());
    }
  }

  postTypeProductCompensation(payload: any, id?: string) {
    if (id) {
      return this.baseService.putData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.EDIT.PRODUCT_COMPENSATION + id, payload).pipe(shareReplay());
    } else {
      return this.baseService.postData<IResponseProgramMarketing>(API.PROGRAM_MARKETING.CREATE.PRODUCT_COMPENSATION, payload).pipe(shareReplay());
    }
  }

  getSalesDiscount(scope: string, id: string[]) {
    let type = '';
    switch (scope) {
      case EnumProgramMarketingScope.DISTRIBUTOR: {
        type = 'distributor_id';
        break;
      }
      case EnumProgramMarketingScope.AREA: {
        type = 'area_id';
        break;
      }
      case EnumProgramMarketingScope.SUB_AREA: {
        type = 'sub_area_id';
        break;
      }
      default: {
        type = 'area_id';
        break;
      }
    }
    const params = `?${type}=${id.join(',')}`;
    this.baseService.getData<ISalesDiscountProgramMarketing>(API.PROGRAM_MARKETING.GET_MAX_SALES_DISCOUNT + params).subscribe((res) => {
      if (res && res.success) {
        this.salesDiscount = res.data;
      }
    });
  }

  getUpdateOneShoot(id: string) {
    return this.baseService.getData(API.PROGRAM_MARKETING.UPDATE_DATA.ONE_SHOOT + id).pipe(shareReplay());
  }

  getUpdateProductDiscount(id: string) {
    return this.baseService.getData(API.PROGRAM_MARKETING.UPDATE_DATA.PRODUCT_DISCOUNT + id).pipe(shareReplay());
  }

  getUpdateDiscountPurchase(id: string) {
    return this.baseService.getData(API.PROGRAM_MARKETING.UPDATE_DATA.DISCOUNT_PURCHASE + id);
  }

  getUpdateCompensation(id: string) {
    return this.baseService.getData(API.PROGRAM_MARKETING.UPDATE_DATA.PRODUCT_COMPENSATION + id).pipe(shareReplay());
  }

  addRemoveControlValidators(controls: FormControl[], mode: 'ADD' | 'CLEAR', form?: FormGroup) {
    switch (mode) {
      case 'ADD':
        controls.forEach((ctrl) => {
          ctrl.addValidators(Validators.required);
          ctrl.updateValueAndValidity();
        });
        break;
      case 'CLEAR':
        controls.forEach((ctrl) => {
          ctrl.clearValidators();
          ctrl.updateValueAndValidity();
        });
        break;
    }
    form?.updateValueAndValidity();
  }

  maxValueDecimalValidator(maxValue: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const _value = this.utils.stringNumberToPayload(control.value).toString();
      const normalizedValue = parseFloat(_value.replace(',', '.'));
      if (isNaN(normalizedValue)) return null;
      return normalizedValue > maxValue ? { max: { max: maxValue, actual: normalizedValue } } : null;
    };
  }

  minDiscountValidator(value: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const normalizedValue = parseFloat(control.value.replace(',', '.'));
      return normalizedValue <= value ? { min: { min: value, actual: normalizedValue } } : null;
    };
  }

  maxDiscountValidator(maxValue: number, type: EnumProgramMarketingDiscountCategory): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const _value = this.utils.stringNumberToPayload(control.value).toString();
      const normalizedValue =
        type === EnumProgramMarketingDiscountCategory.PERCENTAGE
          ? parseFloat(_value.replace(',', '.'))
          : // Remove thousand separator & replace decimal
            Number(_value.replace(/\./g, '').replace(',', '.'));
      if (isNaN(normalizedValue)) return null;
      return normalizedValue > maxValue ? { max: { max: maxValue, actual: normalizedValue } } : null;
    };
  }

  formatInternationalNumber(value: string) {
    if (!value) return 0;
    return this.utils.formatInternationalNumber(value);
  }

  reverseFormatInternationalNumber(value: number) {
    if (isNaN(value) || value === null) return '0';
    return this.utils.reverseFormatInternationalNumber(value);
  }

  getNationalArea() {
    return this.baseService.getData(API.PROGRAM_MARKETING.GET_NATIONAL_AREA).pipe(shareReplay());
  }
}
