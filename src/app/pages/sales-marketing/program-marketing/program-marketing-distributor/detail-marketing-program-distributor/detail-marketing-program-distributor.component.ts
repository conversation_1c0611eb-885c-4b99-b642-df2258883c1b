import { Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy/program-marketing-legacy.service';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import {
  DiscountTerm__DiscountProduct,
  IPayloadProgramVerification,
  IRevisionNote,
  IPayloadExtendPeriod,
} from '../../../program-marketing-legacy/program-marketing-legacy.interface';
import {
  EnumProgramMarketingDiscountCategoryString,
  EnumProgramMarketingDiscountTypeString,
  EnumProgramMarketingPOType,
  EnumProgramMarketingPOTypeString,
  EnumProgramMarketingSection,
  EnumProgramMarketingStatus,
  EnumProgramMarketingType,
  EnumVerificationStatus,
} from '../../../program-marketing-legacy/program-marketing.enum';
import { ContentDetailProgramComponent } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/detail-marketing-program-distributor/components/content-detail-program/content-detail-program.component';
import { ContentListPOComponent } from '../../../program-marketing-legacy/detail-program-marketing/components/content-list-po/content-list-po.component';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { TableColumn } from '@shared/interface/table.interface';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ContentListSpmComponent } from '../../../program-marketing-legacy/detail-program-marketing/components/content-list-spm/content-list-spm.component';
import { ModalExtendPeriodComponent } from '@pages/sales-marketing/program-marketing-legacy/components/modal-extend-period/modal-extend-period.component';
import { IProgramMarketingDetail } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor-interface';
import { ProgramMarketingDistributorService } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor.service';

@Component({
  selector: 'app-detail-program-marketing',
  templateUrl: './detail-marketing-program-distributor.component.html',
  styleUrls: ['./detail-marketing-program-distributor.component.scss'],
})
export class DetailMarketingProgramDistributorComponent implements OnInit, OnDestroy {
  @HostBinding('class') class = 'h-100';

  links!: PageLink[];
  programID!: string;
  isFirstInit = false; // to track first page load
  isLoadingSubject = new BehaviorSubject(false);

  detailProgram!: IProgramMarketingDetail;
  detailProgramMarketing$: Observable<IProgramMarketingDetail | null>;
  revisionNoteData!: IPayloadProgramVerification;

  @ViewChild('modalExtendPeriod') modalExtendPeriod: ModalExtendPeriodComponent;

  @ViewChild('modalConfirmSubmission') modalConfirmSubmission!: ModalComponent;
  modalConfigConfirm: ModalConfig = {
    modalTitle: 'Konfirmasi Program Marketing',
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Lanjutkan',
    onDismiss: () => this.postSubmission(),
  };

  @ViewChild('modalResponseSubmission') modalResponseSubmission: ModalComponent;
  modalConfigResponse: ModalConfig = {
    closeButtonLabel: 'Lihat Detail',
    dismissButtonLabel: 'Oke',
    onClose: () => this.onCloseModalResponse(),
  };

  tableColumns!: TableColumn[];
  tableDiscountProducts!: DiscountTerm__DiscountProduct[];
  displayedColumns!: string[];
  discountPurchaseProducts: SafeHtml;

  responseText!: string;
  private unsubscribe: Subscription[] = [];

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    protected programMarketingService: ProgramMarketingLegacyService,
    protected utils: UtilitiesService,
    protected programDistributorService: ProgramMarketingDistributorService,
    private sanitizer: DomSanitizer
  ) {
    this.detailProgramMarketing$ = this.programDistributorService.detailProgramMarketing$;
    this.links = this.programMarketingService.initProgramMarketingBreadcrumb;
    this.programID = this.activeRoute.snapshot.params['id'];
  }

  get viewWithNoTab() {
    return this.isStatusSubmitted() || this.isStatusNeedChanged();
  }

  get IsTypeCompensation() {
    return this.detailProgram.information.program_type_enum === EnumProgramMarketingType.PRODUCT_COMPENSATION;
  }

  ngOnInit() {
    this.getDetail();
    this.subscriptionDetail();
  }

  getDetail() {
    this.isLoadingSubject.next(true);
    const _dataSubs = this.programDistributorService.getProgramMarketingDetail(this.programID).subscribe((resp) => {
      console.log('getDetail - resp ', resp);
      if (!resp) return;
      this.detailProgram = resp;
      this.initRevisionNoteData();
      this.isLoadingSubject.next(false);
    });

    this.unsubscribe.push(_dataSubs);
  }

  initPageInfo() {
    const _lastIndexOfLink = this.links.length - 1;
    this.links[_lastIndexOfLink].isActive = false;
    const { header } = this.programDistributorService.DetailProgramMarketing;
    this.links.push(
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: header.name,
        path: '',
        isActive: true,
      }
    );

    this.pageInfoService.updateTitle('Detail Program Marketing Distributor');
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  initRevisionNoteData() {
    const { information, order_term, program_term, discount_term, reward, compensation } = this.detailProgram;
    this.revisionNoteData = {
      revision_note: {
        information: information?.revision_note,
        order_term: order_term?.revision_note,
        program_term: program_term?.revision_note,
        discount_term: discount_term?.revision_note,
        reward: reward?.revision_note ?? null, // temp.fix need response reward revision_note
        compensation: compensation?.revision_note,
        order_and_discount_term: null,
      },
    };

    const data = this.hasSessionRevision() ? this.programMarketingService.getSessionRevisionNote() : this.revisionNoteData;
    this.setSessionRevisionNote(data);
  }

  initDiscountProductTable(e: { tableColumns: TableColumn[]; data: DiscountTerm__DiscountProduct[] }) {
    const { tableColumns, data } = e;
    this.tableColumns = tableColumns;
    this.tableDiscountProducts = data;
    this.displayedColumns = tableColumns.map((col) => col.key);
  }

  initDiscountPurchaseData(e: string) {
    this.discountPurchaseProducts = this.sanitizer.bypassSecurityTrustHtml(e);
  }

  hasSessionRevision() {
    const val = this.programMarketingService.getSessionRevisionNote();
    return !!(val && !!Object.keys(val).length);
  }

  setSessionRevisionNote(data?: IPayloadProgramVerification) {
    this.programMarketingService.setSessionRevisionNote(data ?? this.revisionNoteData);
  }

  subscriptionDetail() {
    const _detailSubs = this.detailProgramMarketing$.subscribe((data) => {
      if (data) {
        this.detailProgram = data;
        if (!this.isFirstInit) {
          this.isFirstInit = true;
          return this.initPageInfo();
        }
      }
    });

    this.unsubscribe.push(_detailSubs);
  }

  onNoteChange(e: string, type: EnumProgramMarketingSection) {
    const { revision_note } = this.revisionNoteData;

    // Mapping section type to revision_note keys
    const _section: Record<EnumProgramMarketingSection, keyof IPayloadProgramVerification['revision_note']> = {
      [EnumProgramMarketingSection.INFORMATION]: 'information',
      [EnumProgramMarketingSection.ORDER_TERM]: 'order_term',
      [EnumProgramMarketingSection.PROGRAM_TERM]: 'program_term',
      [EnumProgramMarketingSection.DISCOUNT_TERM]: 'discount_term',
      [EnumProgramMarketingSection.REWARD]: 'reward',
      [EnumProgramMarketingSection.COMPENSATION]: 'compensation',
      [EnumProgramMarketingSection.ORDER_AND_DISCOUNT_TERM]: 'order_and_discount_term',
    };

    if (_section[type]) {
      revision_note[_section[type]] = e;
      return this.setSessionRevisionNote(this.revisionNoteData);
    }
  }

  isStatusSubmitted = () => this.detailProgram.header.status_program_marketing_enum === EnumProgramMarketingStatus.SUBMITTED;

  isStatusNeedChanged = () => this.detailProgram.header.status_program_marketing_enum === EnumProgramMarketingStatus.NEED_CHANGED;

  isStatusActive = () => this.detailProgram.header.status_program_marketing_enum === EnumProgramMarketingStatus.ACTIVE;

  getConfirmCTAState() {
    // enhance: switch program type to check verified sections
    const _type = this.getProgramMarketingType();
    const _sectionStatuses = <(EnumVerificationStatus | null)[]>[];
    switch (_type) {
      case EnumProgramMarketingType.DISCOUNT_PRODUCT:
        _sectionStatuses.push(
          this.validateStatusVerifiedEnum(this.detailProgram.information.status_verified_enum, EnumProgramMarketingSection.INFORMATION),
          this.validateStatusVerifiedEnum(this.detailProgram.program_term.status_verified_enum, EnumProgramMarketingSection.PROGRAM_TERM),
          this.validateStatusVerifiedEnum(this.detailProgram.discount_term.status_verified_enum, EnumProgramMarketingSection.DISCOUNT_TERM)
        );
        break;

      case EnumProgramMarketingType.DISCOUNT_PURCHASE:
        _sectionStatuses.push(
          this.validateStatusVerifiedEnum(this.detailProgram.information.status_verified_enum, EnumProgramMarketingSection.INFORMATION),
          this.validateStatusVerifiedEnum(this.detailProgram.program_term.status_verified_enum, EnumProgramMarketingSection.PROGRAM_TERM)
          // this.validateStatusVerifiedEnum(this.detailProgram.order_term.status_verified_enum, EnumProgramMarketingSection.ORDER_TERM),
          // this.validateStatusVerifiedEnum(this.detailProgram.discount_term.status_verified_enum, EnumProgramMarketingSection.DISCOUNT_TERM)
        );
        break;

      case EnumProgramMarketingType.PRODUCT_COMPENSATION:
        _sectionStatuses.push(
          this.validateStatusVerifiedEnum(this.detailProgram.information.status_verified_enum, EnumProgramMarketingSection.INFORMATION),
          this.validateStatusVerifiedEnum(this.detailProgram.compensation.status_verified_enum, EnumProgramMarketingSection.COMPENSATION)
        );
        break;

      // act as default to ONE_SHOOT because response enum return ONE_SHOT
      default:
        _sectionStatuses.push(
          this.validateStatusVerifiedEnum(this.detailProgram.information.status_verified_enum, EnumProgramMarketingSection.INFORMATION),
          this.validateStatusVerifiedEnum(this.detailProgram.program_term.status_verified_enum, EnumProgramMarketingSection.PROGRAM_TERM),
          this.validateStatusVerifiedEnum(this.detailProgram.order_term.status_verified_enum, EnumProgramMarketingSection.ORDER_TERM),
          this.validateStatusVerifiedEnum(this.detailProgram.discount_term.status_verified_enum, EnumProgramMarketingSection.DISCOUNT_TERM),
          this.validateStatusVerifiedEnum(this.detailProgram.reward.status_verified_enum, EnumProgramMarketingSection.REWARD)
        );
        break;
    }

    // able to confirm when status verified or request revision
    return _sectionStatuses.every(this.programMarketingService.isSectionVerifiedOrRequestRevision);
  }

  validateStatusVerifiedEnum(status: EnumVerificationStatus | null, type: EnumProgramMarketingSection) {
    // if request revision must be have note
    switch (status) {
      case EnumVerificationStatus.REQUEST_REVISION:
        const _haveNote = !!this.revisionNoteData.revision_note[type.toLowerCase() as unknown as keyof IRevisionNote];
        return _haveNote ? EnumVerificationStatus.REQUEST_REVISION : null;
      default:
        return status;
    }
  }

  postSubmission() {
    this.isLoadingSubject.next(true);
    this.modalResponseSubmission.open();

    // if any of revision note has value means program need revision.
    this.responseText = this.revisionNoteHasValues() ? 'Request perbaikan Program Marketing berhasil.' : 'Program Marketing berhasil dikonfirmasi.';
    this.programMarketingService.postSubmitProgramVerification(this.programID, this.revisionNoteData).subscribe((resp) => {
      if (resp && resp.success) {
        this.isLoadingSubject.next(false);
        setTimeout(() => {
          this.programMarketingService.clearSessionRevisionNote();
          this.router.navigate(['/sales-marketing/program-marketing/list']);
        }, 100);
      }
    });

    return true;
  }

  onCloseModalResponse() {
    setTimeout(() => this.router.navigate(['/sales-marketing/program-marketing/' + this.programID]), 100);
    return true;
  }

  revisionNoteHasValues() {
    const data = this.revisionNoteData.revision_note;
    let _check = false;
    for (const key in data) {
      if (data[key as keyof typeof data] !== null && data[key as keyof typeof data] !== undefined) _check = true;
    }
    return _check;
  }

  getTextRevision(section: EnumProgramMarketingSection) {
    const _text = 'Admin perlu memperbaiki';
    const _map: Record<EnumProgramMarketingSection, string> = {
      [EnumProgramMarketingSection.INFORMATION]: ' Informasi Program',
      [EnumProgramMarketingSection.PROGRAM_TERM]: ' Ketentuan Program',
      [EnumProgramMarketingSection.ORDER_TERM]: ' Ketentuan Pembelian',
      [EnumProgramMarketingSection.DISCOUNT_TERM]: ' Ketentuan Diskon',
      [EnumProgramMarketingSection.REWARD]: ' Ketentuan Hadiah',
      [EnumProgramMarketingSection.COMPENSATION]: ' Informasi Kompensasi',
      [EnumProgramMarketingSection.ORDER_AND_DISCOUNT_TERM]: ' Pengaturan Pembelian dan Diskon',
    };

    return _text + _map[section];
  }

  getProgramMarketingType() {
    return !this.utils.isObjectEmpty(this.detailProgram) ? this.detailProgram.information.program_type_enum : this.programMarketingService.ProgramMarketingType;
  }

  getProgramMarketingTitle() {
    return !this.utils.isObjectEmpty(this.detailProgram) ? this.detailProgram.header.name : null;
  }

  getUsePeriod() {
    return this.detailProgram.information.program_type_enum !== EnumProgramMarketingType.PRODUCT_COMPENSATION;
  }

  getPrivilegeConfirm(): boolean {
    return this.programMarketingService.getPrivilegeCTAConfirmProgram();
  }

  getPrivilegeEdit(): boolean {
    return this.programMarketingService.getPrivilegeCTAEditProgram();
  }

  goToEdit() {
    const type = this.programMarketingService.DetailProgramMarketing.information.program_type_enum;
    const _type = (type as unknown as string) === 'ONE_SHOT' ? EnumProgramMarketingType.ONE_SHOOT : type;
    return this.router.navigate(['/sales-marketing/program-marketing/form/' + this.programID], {
      queryParams: {
        type: _type,
        status: this.detailProgram.header.status_program_marketing_enum,
      },
    });
  }

  showExtendsModal() {
    return this.modalExtendPeriod.openModal();
  }

  handleDataExtendPeriod(e: IPayloadExtendPeriod) {
    return e;
  }

  showActions() {
    if (!this.detailProgram) return false;
    const type = [EnumProgramMarketingType.DISCOUNT_PURCHASE, EnumProgramMarketingType.DISCOUNT_PRODUCT].toString();
    return type.includes(this.detailProgram.information.program_type_enum) && this.isStatusActive() && this.getPrivilegeEdit();
  }

  ngOnDestroy() {
    this.programMarketingService.clearSessionRevisionNote();
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly ContentDetailProgramComponent = ContentDetailProgramComponent;
  protected readonly ContentListPOComponent = ContentListPOComponent;
  protected readonly ContentListSpmComponent = ContentListSpmComponent;
  protected readonly EnumProgramMarketingSection = EnumProgramMarketingSection;
  protected readonly EnumProgramMarketingPOTypeString = EnumProgramMarketingPOTypeString;
  protected readonly EnumProgramMarketingPOType = EnumProgramMarketingPOType;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumProgramMarketingDiscountCategoryString = EnumProgramMarketingDiscountCategoryString;
  protected readonly EnumProgramMarketingDiscountTypeString = EnumProgramMarketingDiscountTypeString;
}
