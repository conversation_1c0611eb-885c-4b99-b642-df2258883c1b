<app-fullscreen-loader *ngIf="!detailProgram && (isLoadingSubject | async); else dataLoadedTpl" />

<ng-template #dataLoadedTpl>
  <!--
    card section: catatan perbaikan
    -->
  <app-card-note-correction-program *ngIf="isStatusNeedChanged()" />

  <!--
    switch view by status
    no tab po for: NEED_CHANGED, SUBMITTED.
    other with tab po
  -->
  <ng-container [ngTemplateOutlet]="viewWithNoTab ? viewWithNoTabTpl : viewDefaultTpl"></ng-container>
</ng-template>

<ng-template #viewDefaultTpl>
  <app-mai-tab-group>
    <app-mai-tab [component]="ContentDetailProgramComponent" [title]="'Detail Program'" />
    <app-mai-tab *ngIf="!IsTypeCompensation" [component]="ContentListPOComponent" [title]="'List PO'" />
    <app-mai-tab *ngIf="IsTypeCompensation" [component]="ContentListSpmComponent" [title]="'Surat Perintah Muat'" />
  </app-mai-tab-group>
</ng-template>

<ng-template #viewWithNoTabTpl>
  <app-card-header-detail-program [data]="detailProgram.header" />

  <div class="row">
    <div [ngClass]="isStatusSubmitted() ? 'col-md-8' : 'col-md'" class="col-12">
<!--      <app-card-information-program (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.INFORMATION)" />-->
      <ng-container [ngSwitch]="getProgramMarketingType()">
        <ng-container *ngSwitchCase="EnumProgramMarketingType.ONE_SHOOT" [ngTemplateOutlet]="oneShootTpl"></ng-container>
        <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT" [ngTemplateOutlet]="discountProductTpl"></ng-container>
        <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE" [ngTemplateOutlet]="discountPurchaseTpl"></ng-container>
        <ng-container *ngSwitchCase="EnumProgramMarketingType.PRODUCT_COMPENSATION" [ngTemplateOutlet]="compensationTpl"></ng-container>
        <ng-container *ngSwitchDefault [ngTemplateOutlet]="oneShootTpl"></ng-container>
      </ng-container>
    </div>

    <!-- switch section card by program type -->
    <ng-template #oneShootTpl>
      <app-card-term-program (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.PROGRAM_TERM)" />
      <app-card-order-term (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.ORDER_TERM)" />
      <app-card-discount-term (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.DISCOUNT_TERM)" />
      <app-card-reward-term (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.REWARD)" />
    </ng-template>

    <ng-template #discountProductTpl>
      <app-card-term-program (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.PROGRAM_TERM)" />
      <app-card-discount-term (dataDiscountProducts)="initDiscountProductTable($event)" (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.DISCOUNT_TERM)" />
    </ng-template>

    <ng-template #discountPurchaseTpl>
      <app-card-program-term />
      <app-card-order-and-discount-term [programType]="getProgramMarketingType()"/>
      <!--      <app-card-term-program (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.PROGRAM_TERM)" />-->
      <!--      <app-card-order-term (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.ORDER_TERM)" />-->
      <!--      <app-card-discount-term (dataDiscountPurchase)="initDiscountPurchaseData($event)" (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.DISCOUNT_TERM)" />-->
    </ng-template>

    <ng-template #compensationTpl>
      <app-card-information-compensation (termNoteChanged)="onNoteChange($event, EnumProgramMarketingSection.COMPENSATION)" />
    </ng-template>

    <div *ngIf="isStatusSubmitted()" class="col-12 col-md-4">
      <app-card [cardBodyClasses]="'pt-0'" [cardClasses]="'animation animation-fade-in'" [cardHeaderTitle]="'Dokumen'" [header]="true">
        <ng-container cardBody>
          <app-document-preview [data]="detailProgram.document_url" />
        </ng-container>
      </app-card>
    </div>
  </div>

  <!-- section CTA: finance/admin privilege view - confirm revision -->
  <div *ngIf="isStatusSubmitted() && getPrivilegeConfirm()" class="button-group d-flex align-items-center justify-content-end mb-20">
    <button (click)="modalConfirmSubmission.open()" [disabled]="!getConfirmCTAState()" class="btn btn-primary" color="primary" mat-raised-button type="button">
      <span>Konfirmasi</span>
    </button>
  </div>

  <!-- modal approval confirmation -->
  <ng-container *ngIf="detailProgramMarketing$ | async as detailProgram">
    <app-modal #modalConfirmSubmission [modalConfig]="modalConfigConfirm" [modalOptions]="{ size: 'lg' }">
      <p>
        Apakah yakin mengonfirmasi Program Marketing <span class="fw-bold">{{ getProgramMarketingTitle() }}</span>
        dengan detail:
      </p>

      <div class="mh-500px overflow-scroll mb-4">
        <!-- section information -->
        <ng-container *ngIf="detailProgram.information as information">
          <div class="mb-4">
            <p class="fw-bold mb-0">Informasi Program Marketing</p>
            <div *ngIf="information.status_verified_enum" class="ms-n2">
              <app-section-verified-badge
                [isRequestRevision]="information.status_verified_enum === EnumVerificationStatus.REQUEST_REVISION"
                [sectionVerificationStatus]="information.status_verified_enum"
                [textRevision]="getTextRevision(EnumProgramMarketingSection.INFORMATION)"
              />
            </div>
            <div class="container">
              <div class="row mt-4">
                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Tipe Program</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ information.program_type_string }}</span>
                </div>

                <ng-container *ngIf="getUsePeriod()">
                  <div class="col-12 col-md-4 mb-2">
                    <span class="label text-gray-700">Periode</span>
                  </div>
                  <div class="col-12 col-md-8 mb-4">
                    <span class="me-1">:</span>
                    <span>{{ utils.formatEpochToDate(information.period_start) }} s/d {{ utils.formatEpochToDate(information.period_end) }}</span>
                  </div>
                </ng-container>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Catatan Management</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ information.management_note ? information.management_note : '-' }}</span>
                </div>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Nomor Surat Pengajuan</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ information.reference_number ? information.reference_number : '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container [ngSwitch]="getProgramMarketingType()">
          <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT" [ngTemplateOutlet]="disprodConfirmTpl"></ng-container>
          <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE" [ngTemplateOutlet]="dispurConfirmTpl"></ng-container>
          <ng-container *ngSwitchCase="EnumProgramMarketingType.PRODUCT_COMPENSATION" [ngTemplateOutlet]="compenConfirmTpl"></ng-container>
          <ng-container *ngSwitchDefault [ngTemplateOutlet]="oneshootConfirmTpl"></ng-container>
        </ng-container>
      </div>

      <ng-template #oneshootConfirmTpl>
        <ng-container [ngTemplateOutlet]="programTermContentTpl"></ng-container>
        <ng-container [ngTemplateOutlet]="orderTermContentTpl"></ng-container>
        <ng-container [ngTemplateOutlet]="discountTermContentTpl"></ng-container>
        <ng-container [ngTemplateOutlet]="rewardContentTpl"></ng-container>
      </ng-template>

      <ng-template #disprodConfirmTpl>
        <ng-container [ngTemplateOutlet]="programTermContentTpl"></ng-container>
        <ng-container [ngTemplateOutlet]="discountTermContentTpl"></ng-container>
      </ng-template>

      <ng-template #dispurConfirmTpl>
        <ng-container [ngTemplateOutlet]="programTermContentTpl"></ng-container>
        <pre> wip other card section here </pre>
        <!--        <ng-container [ngTemplateOutlet]="orderTermContentTpl"></ng-container>-->
        <!--        <ng-container [ngTemplateOutlet]="discountTermContentTpl"></ng-container>-->
      </ng-template>

      <ng-template #compenConfirmTpl>
        <ng-container [ngTemplateOutlet]="infoCompensationContentTpl"></ng-container>
      </ng-template>

      <!-- section program term -->
      <ng-template #programTermContentTpl>
        <ng-container *ngIf="detailProgram.program_term as programTerm">
          <div class="mb-4">
            <p class="fw-bold mb-0">Ketentuan Program</p>
            <div *ngIf="programTerm.status_verified_enum" class="ms-n2">
              <app-section-verified-badge
                [isRequestRevision]="programTerm.status_verified_enum === EnumVerificationStatus.REQUEST_REVISION"
                [sectionVerificationStatus]="programTerm.status_verified_enum"
                [textRevision]="getTextRevision(EnumProgramMarketingSection.PROGRAM_TERM)"
              />
            </div>
            <div class="container">
              <div class="row mt-4">
                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Cakupan Program</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ programTerm.scope_string }}</span>
                </div>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Kuota</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <div class="d-flex flex-column">
                    <!--      todo: need enhance render quota - use updated interface (IProgramMarketingDetail__ProgramTerm)-->
<!--                    <ng-container *ngFor="let quota of programTerm.quota; let f = first">-->
<!--                      <div *ngIf="f; else defaultQuota">: {{ quota }}</div>-->
<!--                      <ng-template #defaultQuota>-->
<!--                        <div class="ms-2">{{ quota }}</div>-->
<!--                      </ng-template>-->
<!--                    </ng-container>-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </ng-template>

      <!-- section order term -->
      <ng-template #orderTermContentTpl>
        <ng-container *ngIf="detailProgram.order_term as orderTerm">
          <div class="mb-4">
            <p class="fw-bold mb-0">Ketentuan Pembelian</p>
            <div *ngIf="orderTerm.status_verified_enum" class="ms-n2">
              <app-section-verified-badge
                [isRequestRevision]="orderTerm.status_verified_enum === EnumVerificationStatus.REQUEST_REVISION"
                [sectionVerificationStatus]="orderTerm.status_verified_enum"
                [textRevision]="getTextRevision(EnumProgramMarketingSection.ORDER_TERM)"
              />
            </div>
            <div class="container">
              <div class="row mt-4">
                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Aturan Pembelian</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ utils.mapKeyToString(EnumProgramMarketingPOTypeString, orderTerm.purchase_order_type_enum) }}</span>
                </div>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Produk Dibeli</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <div class="d-flex align-items-top">
                    <span class="me-1">:</span>
                    <div [innerHTML]="programMarketingService.renderPurchaseOrderProducts(orderTerm.purchase_order_type_enum)"></div>
                  </div>
                </div>

                <ng-container *ngIf="orderTerm.purchase_order_type_enum === EnumProgramMarketingPOType.ACCUMULATION">
                  <div class="col-12 col-md-4 mb-2">
                    <span class="label text-gray-700">Minimal Pembelian</span>
                  </div>
                  <div class="col-12 col-md-8 mb-4">
                    <span class="me-1">:</span>
                    <span>{{ programMarketingService.MinimumBuy }}</span>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </ng-container>
      </ng-template>

      <!-- section discount term -->
      <ng-template #discountTermContentTpl>
        <ng-container *ngIf="detailProgram.discount_term as discountTerm">
          <div class="mb-4">
            <p class="fw-bold mb-0">Ketentuan Diskon</p>
            <div *ngIf="discountTerm.status_verified_enum" class="ms-n2">
              <app-section-verified-badge
                [isRequestRevision]="discountTerm.status_verified_enum === EnumVerificationStatus.REQUEST_REVISION"
                [sectionVerificationStatus]="discountTerm.status_verified_enum"
                [textRevision]="getTextRevision(EnumProgramMarketingSection.DISCOUNT_TERM)"
              />
            </div>

            <div class="container">
              <div class="row mt-4">
                <ng-container [ngSwitch]="getProgramMarketingType()">
                  <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT" [ngTemplateOutlet]="discountProductConfirmTpl"></ng-container>
                  <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE" [ngTemplateOutlet]="discountPurchaseConfirmTpl"></ng-container>
                  <ng-container *ngSwitchDefault [ngTemplateOutlet]="oneShootConfirmTpl"></ng-container>
                </ng-container>
              </div>
            </div>
          </div>

          <!--oneShootConfirmTpl-->
          <ng-template #oneShootConfirmTpl>
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Cakupan Diskon</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <span class="me-1">:</span>
              <span>{{ discountTerm.discount_scope }}</span>
            </div>
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Diskon Program</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <span class="me-1">:</span>
              <span>{{ discountTerm.discount_purchases }}</span>
            </div>
          </ng-template>

          <!--discountProductConfirmTpl-->
          <ng-template #discountProductConfirmTpl>
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Tipe Diskon</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <span class="me-1">:</span>
              <span>{{ utils.mapKeyToString(EnumProgramMarketingDiscountCategoryString, discountTerm.discount_category) ?? '-' }}</span>
            </div>

            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Jenis Diskon</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <span class="me-1">:</span>
              <span>{{ utils.mapKeyToString(EnumProgramMarketingDiscountTypeString, discountTerm.discount_type_enum) ?? '-' }}</span>
            </div>

            <div class="table-responsive">
              <table [dataSource]="tableDiscountProducts" class="table w-100 gy-5 table-row-bordered align-middle" mat-table>
                <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
                  <th *matHeaderCellDef class="px-3" mat-header-cell>
                    {{ tableColumn.title }}
                  </th>

                  <td *matCellDef="let element" class="px-3">
                    <ng-container [ngSwitch]="tableColumn.key">
                      <div *ngSwitchDefault>
                        <span>{{ element[tableColumn.key] ?? '-' }}</span>
                      </div>
                    </ng-container>
                  </td>
                </ng-container>
                <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
                <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
              </table>
            </div>
          </ng-template>

          <!--discountPurchaseConfirmTpl-->
          <ng-template #discountPurchaseConfirmTpl>
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Tipe Diskon</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <span class="me-1">:</span>
              <span>{{ utils.mapKeyToString(EnumProgramMarketingDiscountCategoryString, discountTerm.discount_category) ?? '-' }}</span>
            </div>
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Diskon</span>
            </div>
            <div class="col-12 col-md-8 mb-4 d-flex">
              <span class="me-1">:</span>
              <div [innerHTML]="discountPurchaseProducts" class="d-flex flex-wrap flex-column"></div>
            </div>
          </ng-template>
        </ng-container>
      </ng-template>

      <!-- section reward -->
      <ng-template #rewardContentTpl>
        <ng-container *ngIf="detailProgram.reward as reward">
          <div class="mb-4">
            <p class="fw-bold mb-0">Ketentuan Hadiah</p>
            <div *ngIf="reward.status_verified_enum" class="ms-n2">
              <app-section-verified-badge
                [isRequestRevision]="reward.status_verified_enum === EnumVerificationStatus.REQUEST_REVISION"
                [sectionVerificationStatus]="reward.status_verified_enum"
                [textRevision]="getTextRevision(EnumProgramMarketingSection.REWARD)"
              />
            </div>

            <div class="container">
              <div class="row mt-4">
                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Aturan Hadiah</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ reward.multiplication_type ? 'Berlaku Kelipatan' : 'Tidak Berlaku Kelipatan' }}</span>
                </div>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Produk Hadiah</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <div class="d-flex align-items-top">
                    <span class="me-1">:</span>
                    <span>{{ programMarketingService.renderPurchaseOrderRewards(reward.reward_type_enum) }}</span>
                  </div>
                </div>
                <ng-container *ngIf="programMarketingService.isProgramRewardNonMAI">
                  <div class="col-12 col-md-4 mb-2">
                    <span class="label text-gray-700">Maksimal Budget</span>
                  </div>
                  <div class="col-12 col-md-8 mb-4">
                    <div class="d-flex align-items-top">
                      <span class="me-1">:</span>
                      <span>{{ utils.toRupiah(reward.non_mai_product?.maximum_budget_other_reward) }}</span>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </ng-container>
      </ng-template>

      <!-- section information compensation -->
      <ng-template #infoCompensationContentTpl>
        <ng-container *ngIf="detailProgram.compensation as compensation">
          <div class="mb-4">
            <p class="fw-bold mb-0">Informasi Kompensasi</p>
            <div *ngIf="compensation.status_verified_enum" class="ms-n2">
              <app-section-verified-badge
                [isRequestRevision]="compensation.status_verified_enum === EnumVerificationStatus.REQUEST_REVISION"
                [sectionVerificationStatus]="compensation.status_verified_enum"
                [textRevision]="getTextRevision(EnumProgramMarketingSection.COMPENSATION)"
              />
            </div>
            <div class="container">
              <div class="row mt-4">
                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Distributor</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ compensation.distributor_name }}</span>
                </div>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Alamat Pengiriman</span>
                </div>
                <div class="col-12 col-md-8 mb-4">
                  <span class="me-1">:</span>
                  <span>{{ compensation.delivery_address || (compensation.delivery_address_id ?? '-') }}</span>
                </div>

                <div class="col-12 col-md-4 mb-2">
                  <span class="label text-gray-700">Produk Kompensasi</span>
                </div>
                <div class="col-12 col-md-8 mb-4 d-flex">
                  <span class="me-1">:</span>
                  <div class="d-flex flex-wrap flex-column">
                    <span *ngFor="let product of compensation.products">
                      {{ product.variant_name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </ng-template>
    </app-modal>

    <!-- modal response -->
    <app-modal #modalResponseSubmission [modalConfig]="modalConfigResponse">
      <div class="d-flex flex-column justify-content-center align-items-center mb-n8">
        <ng-container *ngIf="isLoadingSubject | async; else responseMessageTpl">
          <div class="mt-8">
            <mat-spinner></mat-spinner>
          </div>
        </ng-container>
        <ng-template #responseMessageTpl>
          <div class="mt-8">
            <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
          </div>
          <div class="mb-5">
            {{ responseText }}
          </div>
        </ng-template>
      </div>
    </app-modal>
  </ng-container>
</ng-template>

<app-modal-extend-period
  #modalExtendPeriod
  [startDateTime]="1756684799000"
  [endDateTime]="1756684799000"
  (payloadExtend)="handleDataExtendPeriod($event)"
></app-modal-extend-period>

<div [ngClass]="showActions() ? 'd-block' : 'd-none'" class="d-flex align-items-center position-absolute top-0 end-0 m-9 mt-6">
  <a class="btn btn-outline btn-outline-primary ps-7 mb-2" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end" data-kt-menu-trigger="click"
    >Actions
    <span class="svg-icon svg-icon-2 me-0">
      <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
          fill="currentColor"
        />
      </svg>
    </span>
  </a>

  <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold py-4 w-250px fs-6" data-kt-menu="true">
    <div class="menu-item px-5">
      <a (click)="goToEdit()" class="menu-link">
        <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PEN_EDIT" class="svg-icon svg-icon-2 me-3"></span>
        <span style="width: max-content">Edit Program</span>
      </a>
      <!--      <a (click)="showExtendsModal()" class="menu-link">-->
      <!--        <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_TANGGAL_KALENDER_CALENDAR" class="svg-icon svg-icon-2 me-3"></span>-->
      <!--        <span style="width: max-content">Perpanjang Periode Program</span>-->
      <!--      </a>-->
    </div>
  </div>
</div>
