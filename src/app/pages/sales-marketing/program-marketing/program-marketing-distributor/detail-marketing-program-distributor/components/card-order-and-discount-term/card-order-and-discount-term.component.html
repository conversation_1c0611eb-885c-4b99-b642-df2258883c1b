<app-card-section-loader *ngIf="isLoadingSubject | async; else cardDataTpl" />

<ng-template #cardDataTpl>
  <ng-container *ngIf="(detailProgramMarketing$|async) as detailProgram">
<!--    <pre>detailProgram: {{ detailProgram | json }}</pre>-->
    <pre>program type: {{ programType}}</pre>
    <app-card [cardBodyClasses]="'pt-0'"
              [cardClasses]="(isSectionVerified() ? 'border border-primary' : isSectionRequestRevision() ? 'border border-danger' : '') + ' mb-8 animation animation-fade-in'"
              [header]="true"
    >

      <ng-container [ngSwitch]="programType">
        <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE">
          <ng-container cardHeader>
            <h6 class="fw-bolder d-flex w-100 align-items-center mb-0">
              <span>Pengaturan Pembelian & Diskon</span>
            </h6>
          </ng-container>
          <ng-container cardBody>
            <div class="row">
              <!-- aturan pembelian -->
              <div class="col-12 col-md-4 mb-2">
                <span class="label text-gray-700">Aturan Pembelian 1 PO</span>
              </div>

              <div class="col-12 col-md-8 mb-4">
                <span class="me-1">:</span>
                <span class="text-capitalize"> {{ detailProgram.order_term.minimum_purchase }} </span>
              </div>

              <div class="col-12 m-0"><hr class="border-gray-300 my-6" /></div>

              <!-- product on schema_promotions -->
              <ng-container *ngIf="detailProgram.schema_promotions && detailProgram.schema_promotions.length > 0">
                <ng-container *ngFor="let product of detailProgram.schema_promotions[0].order_product; last as isLast">
                  <div class="col-12 mb-4"><span class="fw-bolder">{{ product.name }}</span></div>
                  <ng-container *ngIf="product.qty">
                    <div class="col-12 col-md-4 mb-2">
                      <span class="label text-gray-700">Minimal Pembelian</span>
                    </div>
                    <div class="col-12 col-md-8 mb-4">
                      <span class="me-1">:</span>
                      <span class="text-capitalize">{{ product.qty }}</span>
                    </div>
                  </ng-container>

                  <div class="col-12 col-md-4 mb-2">
                    <span class="label text-gray-700">Diskon</span>
                  </div>
                  <div class="col-12 col-md-8 mb-4">
                    <span class="me-1">:</span>
                    <span class="text-capitalize">{{ product.discount }}</span>
                  </div>
                  <div class="col-12 m-0" *ngIf="!isLast"><hr class="border-gray-300 my-6" /></div>
                </ng-container>
              </ng-container>
            </div>
          </ng-container>
        </ng-container>

        <ng-container *ngSwitchDefault>
          <ng-container cardHeader>
            <h6 class="fw-bolder d-flex w-100 align-items-center mb-0">
              <span>Program Type Lainnya</span>
            </h6>
          </ng-container>
          <ng-container cardBody>
            <pre>wip: template for other type</pre>
          </ng-container>
        </ng-container>
      </ng-container>

    </app-card>
  </ng-container>
</ng-template>
