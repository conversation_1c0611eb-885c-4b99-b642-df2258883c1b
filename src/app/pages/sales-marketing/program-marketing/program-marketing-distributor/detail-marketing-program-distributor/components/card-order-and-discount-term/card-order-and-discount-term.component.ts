import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DistributorModule } from '@pages/distributor/distributor.module';
import { BaseComponent } from '@shared/base/base.component';
import { BehaviorSubject, Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';
import { CardSectionLoaderComponent } from '@shared/components/loader/card-section-loader/card-section-loader.component';
import { IProgramMarketingDetail } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor-interface';
import { ProgramMarketingDistributorService } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor.service';
import { EnumProgramMarketingType } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';

@Component({
  selector: 'app-card-order-and-discount-term',
  standalone: true,
  imports: [DistributorModule, CardSectionLoaderComponent],
  templateUrl: './card-order-and-discount-term.component.html',
  styleUrl: './card-order-and-discount-term.component.scss',
})
export class CardOrderAndDiscountTermComponent extends BaseComponent implements OnInit, AfterViewInit {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  programID!: string;

  detailProgramMarketing$!: Observable<IProgramMarketingDetail>;
  @Input() programType: EnumProgramMarketingType;
  @Output() termNoteChanged = new EventEmitter<string>();

  constructor(
    private activatedRoute: ActivatedRoute,
    private programDistributorService: ProgramMarketingDistributorService<IProgramMarketingDetail>,
    protected utils: UtilitiesService
  ) {
    super();
    this.detailProgramMarketing$ = this.programDistributorService.detailProgramMarketing$;
  }

  ngOnInit(): void {
    this.subscriptionDetail();
  }

  ngAfterViewInit(): void {}

  subscriptionDetail() {
    this.isLoadingSubject.next(true);
    // this.detailProgramMarketing$ = this.programDistributorService.detailProgramMarketing$.pipe(
    //   this.takeUntilDestroy(),
    //   map((legacy: IProgramMarketingDetailLegacy & { schema_promotions?: any[] }) => {
    //     const converted: IProgramMarketingDetailDiscountPurchase = {
    //       ...legacy,
    //       schema_promotions: legacy?.schema_promotions ?? [],
    //       order_term: legacy.order_term as unknown as IProgramMarketingDetailDiscountPurchase__OrderTerm, // override
    //     };
    //     return converted;
    //   })
    // );

    this.detailProgramMarketing$.subscribe({
      next: (resp) => {
        if (resp) {
          this.isLoadingSubject.next(false);
          // this.handleSessionInput();
        }
      },
      error: () => this.isLoadingSubject.next(false),
    });
  }

  isStatusSubmitted = () => this.programDistributorService.isProgramStatusSubmitted;

  isSectionVerified() {
    const _statusEnum = this.programDistributorService.DetailProgramMarketing.program_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programDistributorService.isSectionVerified(_statusEnum);
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programDistributorService.DetailProgramMarketing.program_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programDistributorService.isSectionRequestRevision(_statusEnum);
  }

  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
}
