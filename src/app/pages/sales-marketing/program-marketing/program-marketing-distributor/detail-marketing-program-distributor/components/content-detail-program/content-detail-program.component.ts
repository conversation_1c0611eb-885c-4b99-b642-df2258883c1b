import { Component, OnInit } from '@angular/core';
import { ProgramMarketingLegacyService } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.service';
import { Observable, of } from 'rxjs';
import { IProgramMarketingDetailLegacy } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.interface';
import { EnumProgramMarketingSection, EnumProgramMarketingType } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';

@Component({
  selector: 'app-content-detail-program',
  templateUrl: './content-detail-program.component.html',
  styleUrls: ['./content-detail-program.component.scss'],
})
export class ContentDetailProgramComponent implements OnInit {
  detailProgram$: Observable<IProgramMarketingDetailLegacy>;

  constructor(private programMarketingService: ProgramMarketingLegacyService) {}

  ngOnInit(): void {
    this.detailProgram$ = of(this.programMarketingService.DetailProgramMarketing);
  }

  getProgramType(type: string) {
    return (type as unknown as string) === 'ONE_SHOT' ? EnumProgramMarketingType.ONE_SHOOT : type;
  }

  showCreateSpm(type: string, isFulFilled: boolean = false) {
    const _type = this.getProgramType(type) === EnumProgramMarketingType.PRODUCT_COMPENSATION;
    return _type && !isFulFilled;
  }

  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumProgramMarketingSection = EnumProgramMarketingSection;
}
