<app-card *ngIf="isLoadingSubject | async; else cardDataTpl" [cardClasses]="'mb-8 animation animation-fade-in'">
  <ng-container cardBody>program term card<app-section-loader /></ng-container>
</app-card>
<ng-template #cardDataTpl>
  <ng-container *ngIf="(detailProgramMarketing$ | async)?.program_term as programTerm">
    <app-card
      [cardBodyClasses]="'pt-0'"
      [cardClasses]="(isSectionVerified() ? 'border border-primary' : isSectionRequestRevision() ? 'border border-danger' : '') + ' mb-8 animation animation-fade-in'"
      [header]="true"
    >
      <ng-container cardHeader>
        <h6 class="fw-bolder d-flex w-100 align-items-center mb-0">
          <span>Ketentuan Program</span>
          <app-section-verified-badge *ngIf="showVerifiedBadge()" [isRequestRevision]="isSectionRequestRevision()" [sectionVerificationStatus]="getVerificationStatus()" />
        </h6>
      </ng-container>
      <ng-container cardBody>
        <div class="row">
          <!-- cakupan -->
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Cakupan</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ programTerm.scope_string }}</span>
          </div>

          <!-- kuota -->
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Kuota</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ utils.mapKeyToString(EnumProgramMarketingQuotaTypeString, programTerm.quota.enum_type)}}</span>
            <!--- render quota scope list -->
            <div *ngIf="programTerm.quota.enum_type === EnumProgramMarketingQuotaType.PER_SCOPE"></div>
            <!-- todo: need enhance render quota - use updated interface (IProgramMarketingDetail__ProgramTerm)-->
<!--            <div class="d-flex flex-column">-->
<!--              <ng-container *ngFor="let quota of programTerm.quota; let f = first">-->
<!--                <div *ngIf="f; else defaultQuota">: {{ quota }}</div>-->
<!--                <ng-template #defaultQuota>-->
<!--                  <div class="ms-2">{{ quota }}</div>-->
<!--                </ng-template>-->
<!--              </ng-container>-->
<!--            </div>-->
          </div>

          <!-- program inclusion -->
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Perhitungan Program Long Term Distributor</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ utils.mapKeyToString(EnumInclusionProgramTermTypeString, programTerm.long_term_distributor_inclusion) }}</span>
          </div>
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Perhitungan Program Retailer</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ utils.mapKeyToString(EnumInclusionProgramTermTypeString, programTerm.retailer_inclusion) }}</span>
            <div *ngIf="programTerm.retailer_inclusion === EnumInclusionProgramTermType.EXCLUDE_PARTIAL">
              <ul>
                <li *ngFor="let program of programTerm.excluded_retailer_program" class="mt-2">{{ program.name }}</li>
              </ul>
            </div>
          </div>

          <!-- revision note -->
          <app-input-revision-note #inputRevision (noteChanged)="onNoteChanged($event)" *ngIf="isSectionRequestRevision()" />

          <!--
            CTA: verify, request revision, reset verification
            show only on status submitted - for admin/finance
          -->
          <app-button-group-verify
            *ngIf="isStatusSubmitted()"
            [sectionStatusVerification]="getVerificationStatus()"
            [showBtnVerify]="true"
            [showBtnRequestRevision]="true"
            (ctaVerification)="handleActionVerification($event)"
          />
<!--          [showBtnVerify]="showBtnGroup(EnumVerificationStatus.VERIFIED)"-->
<!--          [showBtnRequestRevision]="showBtnGroup(EnumVerificationStatus.REQUEST_REVISION)"-->
        </div>
      </ng-container>
    </app-card>
  </ng-container>
</ng-template>
