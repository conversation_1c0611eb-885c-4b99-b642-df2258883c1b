import { AfterViewInit, Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { BehaviorSubject, Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { ProgramMarketingLegacyService } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.service';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';
import { DistributorModule } from '@pages/distributor/distributor.module';
import {
  EnumInclusionProgramTermDistributorString,
  EnumInclusionProgramTermRetailerString,
  EnumInclusionProgramTermType,
  EnumInclusionProgramTermTypeString,
  EnumProgramMarketingQuotaType,
  EnumProgramMarketingQuotaTypeString,
  EnumProgramMarketingSection,
  EnumVerificationStatus,
} from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { UtilitiesService } from '@services/utilities.service';
import { IProgramMarketingDetail } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor-interface';
import { ProgramMarketingDistributorService } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor.service';

@Component({
  selector: 'app-card-program-term',
  standalone: true,
  imports: [DistributorModule],
  templateUrl: './card-program-term.component.html',
})
export class CardProgramTermComponent extends BaseComponent implements OnInit, AfterViewInit {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  programID!: string;

  detailProgramMarketing$!: Observable<IProgramMarketingDetail>;
  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @Output() termNoteChanged = new EventEmitter<string>();

  constructor(
    private activatedRoute: ActivatedRoute,
    private programMarketingService: ProgramMarketingLegacyService,
    protected programDistributorService: ProgramMarketingDistributorService,
    protected utils: UtilitiesService
  ) {
    super();
    this.programID = this.activatedRoute.snapshot.params.id;
    this.detailProgramMarketing$ = this.programDistributorService.detailProgramMarketing$;
  }

  ngOnInit(): void {
    this.subscriptionDetail();
  }

  ngAfterViewInit(): void {}

  subscriptionDetail() {
    this.isLoadingSubject.next(true);
    this.detailProgramMarketing$.pipe(this.takeUntilDestroy()).subscribe((data) => {
      if (data) {
        this.isLoadingSubject.next(false);
        this.handleSessionInput();
      }
    });
  }

  updateVerification(status: EnumVerificationStatus | null) {
    const _payload = {
      type: EnumProgramMarketingSection.PROGRAM_TERM,
      status: status,
    };

    this.isLoadingSubject.next(true);
    this.programMarketingService.updateSectionVerification(this.programID, _payload).subscribe((resp) => {
      if (resp && resp.success) this.fetchUpdatedDetailData();
    });
  }

  fetchUpdatedDetailData() {
    this.programMarketingService.getProgramMarketingDetail(this.programID).subscribe((resp) => {
      if (resp) this.programMarketingService.DetailProgramMarketing = resp;
    });
  }

  isStatusSubmitted = () => this.programMarketingService.isProgramStatusSubmitted;

  isSectionVerified() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.program_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionVerified(_statusEnum);
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.program_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionRequestRevision(_statusEnum);
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) return (this.showInputRevisionNote = false);
    return this.toggleRevisionNote();
  }

  toggleRevisionNote = () => (this.showInputRevisionNote = !this.showInputRevisionNote);

  getVerificationStatus = () => this.programMarketingService.DetailProgramMarketing.program_term.status_verified_enum as EnumVerificationStatus;

  showVerifiedBadge = () => this.isStatusSubmitted() && this.programMarketingService.DetailProgramMarketing.program_term.status_verified_enum !== null;

  onNoteChanged = (e: string) => this.termNoteChanged.emit(e?.trim());

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    const _sessionValue = this.programMarketingService.getSessionRevisionNote()?.revision_note;
    if (!!(_sessionValue && !!Object.keys(_sessionValue).length)) setTimeout(() => this.inputRevision.noteControl.setValue(_sessionValue.program_term));
  }

  showBtnGroup(type: EnumVerificationStatus) {
    if (type === EnumVerificationStatus.VERIFIED) return this.programMarketingService.getPrivilegeCTAVerifyProgram();
    return this.programMarketingService.getPrivilegeCTARequestRevision();
  }

  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumInclusionProgramTermDistributorString = EnumInclusionProgramTermDistributorString;
  protected readonly EnumInclusionProgramTermRetailerString = EnumInclusionProgramTermRetailerString;
  protected readonly EnumInclusionProgramTermTypeString = EnumInclusionProgramTermTypeString;
  protected readonly EnumInclusionProgramTermType = EnumInclusionProgramTermType;
  protected readonly EnumProgramMarketingQuotaTypeString = EnumProgramMarketingQuotaTypeString;
  protected readonly EnumProgramMarketingQuotaType = EnumProgramMarketingQuotaType;
}
