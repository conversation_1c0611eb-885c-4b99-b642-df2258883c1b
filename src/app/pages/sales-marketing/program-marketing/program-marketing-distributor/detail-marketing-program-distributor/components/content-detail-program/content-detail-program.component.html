<ng-container *ngIf="detailProgram$ | async as detailProgram">
  <div class="overflow-hidden">
    <app-card-header-detail-program [data]="detailProgram.header" />
    <app-card-information-program />
    <ng-container [ngSwitch]="getProgramType(detailProgram.information.program_type_enum)">
      <ng-container *ngSwitchCase="EnumProgramMarketingType.ONE_SHOOT" [ngTemplateOutlet]="oneShootViewTpl"></ng-container>
      <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT" [ngTemplateOutlet]="discountProductViewTpl"></ng-container>
      <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE" [ngTemplateOutlet]="discountPurchaseViewTpl"></ng-container>
      <ng-container *ngSwitchCase="EnumProgramMarketingType.PRODUCT_COMPENSATION" [ngTemplateOutlet]="compensationViewTpl"></ng-container>
    </ng-container>

    <ng-template #oneShootViewTpl>
      <app-card-term-program />
      <app-card-order-term />
      <app-card-discount-term />
      <app-card-reward-term />
    </ng-template>

    <ng-template #discountProductViewTpl>
      <app-card-term-program />
      <app-card-discount-term />
    </ng-template>

    <ng-template #discountPurchaseViewTpl>
      <app-card-program-term />
      <app-card-order-and-discount-term />
      <!--      <app-card-term-program />-->
      <!--      <app-card-order-term />-->
      <!--      <app-card-discount-term />-->
    </ng-template>

    <ng-template #compensationViewTpl>
      <app-card-information-compensation />
    </ng-template>

    <div *ngIf="showCreateSpm(detailProgram.information.program_type_enum, detailProgram.compensation?.is_fulfilled)" class="button-group">
      <app-modal-warehouse-selector />
    </div>
  </div>
</ng-container>
