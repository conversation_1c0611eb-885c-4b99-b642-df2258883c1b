import { Injectable } from '@angular/core';
import { BehaviorSubject, tap } from 'rxjs';
import { ProgramMarketingDetailModel } from '@pages/sales-marketing/program-marketing/program-marketing-detail.model';
import { EnumProgramMarketingStatus, EnumVerificationStatus } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { BaseService } from '@services/base-service.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { Router } from '@angular/router';
import { API } from '@config/constants/api.constant';
import { map } from 'rxjs/operators';
import { IProgramMarketingDetail } from '@pages/sales-marketing/program-marketing/program-marketing-distributor/program-marketing-distributor-interface';

@Injectable({
  providedIn: 'root',
})
export class ProgramMarketingDistributorService {
  private detailProgramMarketingSubject = new BehaviorSubject<IProgramMarketingDetail>(new ProgramMarketingDetailModel<IProgramMarketingDetail>().Data);
  detailProgramMarketing$ = this.detailProgramMarketingSubject.asObservable();

  get DetailProgramMarketing() {
    return this.detailProgramMarketingSubject.value;
  }

  set DetailProgramMarketing(data: IProgramMarketingDetail) {
    this.detailProgramMarketingSubject.next(data);
  }

  get isProgramStatusSubmitted() {
    return this.DetailProgramMarketing.header.status_program_marketing_enum === EnumProgramMarketingStatus.SUBMITTED;
  }

  get ProgramMarketingType() {
    return this.DetailProgramMarketing && this.DetailProgramMarketing.information.program_type_enum;
  }

  constructor(private baseService: BaseService, private rolePrivilegeService: RolePrivilegeService, private router: Router) {}

  getProgramMarketingDetail(id: string) {
    return this.baseService.getData<IProgramMarketingDetail>(API.PROGRAM_MARKETING.DETAIL_PROGRAM + id).pipe(
      map((resp) => resp && resp.data),
      tap((data) => this.detailProgramMarketingSubject.next(data))
    );
  }

  isSectionVerified = (status?: EnumVerificationStatus | null) => (status ? status === EnumVerificationStatus.VERIFIED : false);

  isSectionRequestRevision = (status?: EnumVerificationStatus | null) => (status ? status === EnumVerificationStatus.REQUEST_REVISION : false);

  isSectionVerifiedOrRequestRevision = (status?: EnumVerificationStatus | null) =>
    status ? status === EnumVerificationStatus.REQUEST_REVISION || status === EnumVerificationStatus.VERIFIED : false;
}
