import { ILegacyProgramMarketingDetail__OrderTerm, IProgramMarketingDetailLegacy } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.interface';
import { IGenericIdName } from '@shared/interface/generic';
import { EnumVerificationStatus } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';

export interface IProgramMarketingDetail extends Omit<IProgramMarketingDetailLegacy, 'order_term'> {
  order_term: IProgramMarketingDetail__OrderTerm;
  schema_promotions: IProgramMarketingDetail__SchemaPromotions[];
}

interface IProgramMarketingDetail__OrderTerm extends Omit<ILegacyProgramMarketingDetail__OrderTerm, 'accumulation' | 'bundling' | 'reward'> {
  order_unit_type: string;
  minimum_purchase: string;
  product_variant: IGenericIdName[];
}

interface IProgramMarketingDetail__SchemaPromotions {
  id: string;
  status_verified_enum: EnumVerificationStatus | null;
  revision_note: string | null;
  order_product: { name: string; qty: string; discount: string }[] | null;
}

// export interface IProgramMarketingDetailDiscountPurchase extends Omit<IProgramMarketingDetail, 'order_term' | 'schema_promotions'> {
//   order_term: IProgramMarketingDetailDiscountPurchase__OrderTerm;
//   schema_promotions: IProgramMarketingDetailDiscountPurchase__SchemaPromotions[];
// }

// DETAIL PROGRAM SECTION CARD
// SCHEMA PROMOTIONS
// interface IProgramMarketingDetailDiscountPurchase__SchemaPromotions extends Omit<IProgramMarketingDetail__SchemaPromotions, 'revision_note' | 'status_verified_enum'> {
//   order_product: { name: string; qty: string; discount: string }[] | null;
// }

// ORDER TERM
// export interface IProgramMarketingDetailDiscountPurchase__OrderTerm extends Omit<ILegacyProgramMarketingDetail__OrderTerm, 'bundling' | 'accumulation' | 'reward'> {
//   minimum_purchase: string;
//   product_variant: IGenericIdName[];
// }
