import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { EnumRetailerProgramSection, IMarketingProgramRetailerDetail, IProgramTerm } from '../../../program-marketing-retailer.interface';
import { BehaviorSubject, Observable } from 'rxjs';
import { EnumVerificationStatus } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { UtilitiesService } from '@services/utilities.service';
import { ProgramMarketingRetailerService } from '../../../program-marketing-retailer.service';
import { AuthService } from '@services/auth.service';
import { EnumProgramRetailerStatusEnum, EnumScopeProgramTerm } from '../../../program-marketing-retailer.enum';

@Component({
  selector: 'app-card-retailer-program-term',
  templateUrl: './card-retailer-program-term.component.html',
  styleUrls: ['./card-retailer-program-term.component.scss'],
})
export class CardRetailerProgramTermComponent implements OnInit {
  @Output() revisionNote = new EventEmitter<string | null>();
  @Output() reqRevision = new EventEmitter<EnumVerificationStatus | null>();

  isVerify: EnumVerificationStatus | null;
  revisionData: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
  isLoading = new BehaviorSubject(false);

  dataInfoProgram$: Observable<IMarketingProgramRetailerDetail>;

  constructor(public utilities: UtilitiesService, private mprService: ProgramMarketingRetailerService, public authService: AuthService) {}

  ngOnInit(): void {
    this.dataInfoProgram$ = this.mprService.initMPRDetailSubject.asObservable();
    this.getDataVerification();
  }

  get isFinance() {
    return this.authService.isRoleFinance || this.authService.isAdminOrSuperAdmin();
  }

  getDataVerification() {
    if (!this.dataInfoProgram$) return {} as IMarketingProgramRetailerDetail;
    return this.dataInfoProgram$.subscribe((data) => {
      const req_rev = data.program_term.revision_note;
      this.isVerify = data.program_term.status_verified_enum;
      this.reqRevision.emit(this.isVerify);
      if (req_rev) {
        this.revisionData.next(req_rev);
        this.revisionNote.emit(req_rev);
      }
    });
  }

  updateVerificationStatus(status: EnumVerificationStatus | null) {
    const payload = {
      status: status,
      section_type: EnumRetailerProgramSection.program_term,
    };
    this.mprService.updateSectionVerification(this.mprService.MprDetailID, payload).subscribe((resp) => {
      if (resp && resp.success) {
        this.isLoading.next(false);
      }
    });
    return true;
  }

  handleNoteChange(e: string | null) {
    this.revisionNote.emit(e);
  }

  handleVerify(e: EnumVerificationStatus | null) {
    this.isVerify = e;
    this.isLoading.next(true);
    this.updateVerificationStatus(e);
    this.reqRevision.emit(e);
  }
  getVerificationStatus = () => this.isVerify as EnumVerificationStatus;

  getScopeOrigin(data: IProgramTerm) {
    if (data.scope_enum === EnumScopeProgramTerm.AREA && Array.isArray(data.scope_string)) {
      return data.scope_string.join(', ');
    }
    return data.scope_string;
  }

  getProductOrigin(data: IProgramTerm) {
    if (data.product_origin_enum === EnumScopeProgramTerm.AREA && Array.isArray(data.product_origin_string)) {
      return data.product_origin_string.join(', ');
    }
    return data.product_origin_string;
  }

  isSectionRequestRevision() {
    return this.isVerify !== EnumVerificationStatus.VERIFIED;
  }

  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumProgramRetailerStatusEnum = EnumProgramRetailerStatusEnum;
}
