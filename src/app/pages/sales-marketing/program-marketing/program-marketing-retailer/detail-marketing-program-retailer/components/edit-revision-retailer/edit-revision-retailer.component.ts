import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { EnumVerificationStatus } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject } from 'rxjs';
import { RolePrivilegeService } from '@services/role-privilege.service';

@Component({
  selector: 'app-edit-revision-retailer',
  templateUrl: './edit-revision-retailer.component.html',
  styleUrls: ['./edit-revision-retailer.component.scss'],
})
export class EditRevisionRetailerComponent implements OnInit {
  @Input() note?: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
  @Output() noteChanged = new EventEmitter<string | null>();
  @Output() ctaVerification = new EventEmitter<EnumVerificationStatus | null>();
  @Input() isRevision?: EnumVerificationStatus | null = null;

  isRequestRevision: boolean = false;
  isVerify: boolean = false;
  form!: FormGroup;

  constructor(private fb: FormBuilder, private rolePrivilegeService: RolePrivilegeService) {
    this.form = this.fb.group({
      note: new FormControl(),
    });
  }

  get noteControl() {
    return <FormControl>this.form.get('note');
  }

  ngOnInit() {
    if (this.isRevision === EnumVerificationStatus.REQUEST_REVISION) {
      this.note?.subscribe((value) => {
        this.noteControl.setValue(value, { emitEvent: false });
      });
      this.isRequestRevision = true;
    }
    this.noteChanges();
  }

  noteChanges() {
    this.noteControl.valueChanges.subscribe((val) => {
      this.noteChanged.emit(val);
    });
  }

  resetNote() {
    this.noteControl.setValue(null);
    this.noteChanged.emit(null);
  }

  getResetValue(): boolean {
    return this.isRevision === EnumVerificationStatus.VERIFIED || this.isRevision === EnumVerificationStatus.REQUEST_REVISION;
  }

  actionVerification(act: 'VERIFY' | 'RESET' | 'REVISION') {
    const _actionEnum = act === 'VERIFY' ? EnumVerificationStatus.VERIFIED : act === 'REVISION' ? EnumVerificationStatus.REQUEST_REVISION : null;
    if (act === 'REVISION') {
      this.resetNote();
      this.isRequestRevision = true;
    } else if (act === 'VERIFY') {
      this.isRequestRevision = true;
      this.isVerify = true;
    } else {
      this.isRequestRevision = false;
      this.isVerify = false;
    }
    return this.ctaVerification.emit(_actionEnum);
  }

  hasPrivilegeVerify() {
    return this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_VERIFY_PROGRAM');
  }

  hasPrivilegeRequest() {
    return this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_REQUEST_REVISION_PROGRAM');
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
