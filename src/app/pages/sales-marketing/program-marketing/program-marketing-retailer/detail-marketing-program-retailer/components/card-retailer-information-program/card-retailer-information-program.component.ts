import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { EnumRetailerProgramSection, IMarketingProgramRetailerDetail } from '../../../program-marketing-retailer.interface';
import { EnumVerificationStatus } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { UtilitiesService } from '@services/utilities.service';
import { ProgramMarketingRetailerService } from '../../../program-marketing-retailer.service';
import { AuthService } from '@services/auth.service';
import { EnumProgramRetailerStatusEnum } from '../../../program-marketing-retailer.enum';

@Component({
  selector: 'app-card-retailer-information-program',
  templateUrl: './card-retailer-information-program.component.html',
  styleUrls: ['./card-retailer-information-program.component.scss'],
})
export class CardRetailerInformationProgramComponent implements OnInit {
  isLoadingSubject = new BehaviorSubject(false);
  @Output() revisionNote = new EventEmitter<string | null>();
  @Output() reqRevision = new EventEmitter<EnumVerificationStatus | null>();

  isVerify: EnumVerificationStatus | null;
  revisionData: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
  dataInfoProgram$: Observable<IMarketingProgramRetailerDetail>;

  constructor(public utilities: UtilitiesService, private mprService: ProgramMarketingRetailerService, public authService: AuthService) {}

  ngOnInit(): void {
    this.dataInfoProgram$ = this.mprService.initMPRDetailSubject.asObservable();
    this.getDataVerification();
  }

  get isFinance() {
    return this.authService.isRoleFinance || this.authService.isAdminOrSuperAdmin();
  }

  getDataVerification() {
    if (!this.dataInfoProgram$) return {} as IMarketingProgramRetailerDetail;
    return this.dataInfoProgram$.subscribe((data) => {
      const req_rev = data.information.revision_note;
      this.isVerify = data.information.status_verified_enum;
      this.reqRevision.emit(this.isVerify);
      if (req_rev) {
        this.revisionData.next(req_rev);
        this.revisionNote.emit(req_rev);
      }
    });
  }

  updateVerificationStatus(status: EnumVerificationStatus | null) {
    const payload = {
      status: status,
      section_type: EnumRetailerProgramSection.information,
    };
    this.mprService.updateSectionVerification(this.mprService.MprDetailID, payload).subscribe((resp) => {
      if (resp && resp.success) {
        this.isLoadingSubject.next(false);
      }
    });
    return true;
  }

  periodeString(date: number) {
    const periode: string = this.utilities.formatEpochToDate(date);
    return periode;
  }

  handleNoteChange(e: string | null) {
    this.revisionNote.emit(e);
  }
  handleVerify(e: EnumVerificationStatus | null) {
    this.isVerify = e;
    this.isLoadingSubject.next(true);
    this.updateVerificationStatus(e);
    this.reqRevision.emit(e);
  }

  getVerificationStatus = () => this.isVerify as EnumVerificationStatus;

  isSectionRequestRevision() {
    return this.isVerify !== EnumVerificationStatus.VERIFIED;
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;

  protected readonly parseInt = parseInt;
  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumProgramRetailerStatusEnum = EnumProgramRetailerStatusEnum;
}
