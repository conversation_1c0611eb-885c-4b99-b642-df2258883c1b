import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ModalDetailConversionComponent } from '../../../components/modal-detail-conversion/modal-detail-conversion.component';
import { ModalConfig, ModalOption } from '@shared/components/modal/modal.interface';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  IConversion,
  IMarketingProgramRetailerDetail,
  IModalRetailerConfirmation,
  IPayloadProgramVerification,
  IModalDetailMPRConversion,
} from '../../../program-marketing-retailer.interface';
import { PageLink } from '@metronic/layout';
import { IProgramMarketingDetailLegacy } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.interface';
import { ProgramMarketingRetailerService } from '../../../program-marketing-retailer.service';
import { ActivatedRoute } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { EnumProgramRetailerStatusEnum } from '../../../program-marketing-retailer.enum';

@Component({
  selector: 'app-detail-with-tab-retailer',
  templateUrl: './detail-with-tab-retailer.component.html',
  styleUrls: ['./detail-with-tab-retailer.component.scss'],
})
export class DetailWithTabRetailerComponent implements OnInit, OnDestroy {
  @ViewChild('modalDetail') private modalDetail: ModalDetailConversionComponent;
  modalDetailConfig: ModalConfig;
  modalDetailOption: ModalOption;
  initDetail: BehaviorSubject<IMarketingProgramRetailerDetail> = new BehaviorSubject<IMarketingProgramRetailerDetail>({} as IMarketingProgramRetailerDetail);
  detailMprConversionData: BehaviorSubject<IModalDetailMPRConversion> = new BehaviorSubject<IModalDetailMPRConversion>({} as IModalDetailMPRConversion);
  detailMprRetailerConfirmation: BehaviorSubject<IModalRetailerConfirmation> = new BehaviorSubject<IModalRetailerConfirmation>({} as IModalRetailerConfirmation);
  conversionData: BehaviorSubject<IConversion[]> = new BehaviorSubject<IConversion[]>({} as IConversion[]);
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  links!: PageLink[];
  programID!: string;

  // Status Cek
  isStatusSubmitted = () => this.detailRetailer.header.status_enum === EnumProgramRetailerStatusEnum.SUBMITTED;

  get enumStatus() {
    return this.detailRetailer.header.status_enum as EnumProgramRetailerStatusEnum;
  }
  detailRetailer!: IMarketingProgramRetailerDetail;

  detailProgramMarketing$: Observable<IProgramMarketingDetailLegacy | null>;
  revisionNoteData!: IPayloadProgramVerification;

  constructor(private mprService: ProgramMarketingRetailerService, private activatedRoute: ActivatedRoute, public utils: UtilitiesService) {}

  ngOnInit(): void {
    this.handleQueryParam();
    this.initPageInfo();
  }

  handleQueryParam() {
    const { id } = this.activatedRoute.snapshot.params;
    this.mprService.MprDetailID = id;
    this.modalDetailConfig = {
      showFooter: false,
      showHeader: false,
    };
    this.modalDetailOption = {
      size: 'md',
    };
  }

  initPageInfo() {
    this.isLoading.next(true);
    this.mprService.getInitialDetailMPR(this.mprService.MprDetailID).subscribe((value) => {
      if (!value) return;
      const { data } = value;
      this.initDetail.next(data);
      this.detailRetailer = data;
      this.conversionData.next(this.initDetail.value?.product_scan_term.conversions ?? ([] as IConversion[]));
      this.setDataDetailConversion();
      this.setDataConfirmation();
      this.isLoading.next(false);
    });
    return;
  }

  setDataDetailConversion() {
    const title: string = 'DETAIL KONVERSI PRODUK';
    const detail: IConversion[] = this.conversionData.value;
    this.detailMprConversionData.next({ title, detail });
  }

  setDataConfirmation() {
    const add: boolean = true;
    const detail: IMarketingProgramRetailerDetail = this.initDetail.value;
    this.detailMprRetailerConfirmation.next({ add, detail });
  }

  handleDetailConversion() {
    return this.modalDetail.openModal();
  }

  ngOnDestroy() {}

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumProgramRetailerStatusEnum = EnumProgramRetailerStatusEnum;
  protected readonly parseInt = parseInt;
}
