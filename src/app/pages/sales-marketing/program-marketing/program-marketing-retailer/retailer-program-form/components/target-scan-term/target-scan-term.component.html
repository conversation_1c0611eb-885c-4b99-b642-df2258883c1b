<h3 class="mb-10 fw-bold">Ketentuan Target Scan</h3>
<ng-container [ngTemplateOutlet]="(isLoadingSubject | async) ? loaderTpl : sectionTpl"></ng-container>

<ng-template #sectionTpl>
  <app-note-view-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? '-'" />
  <div class="form-group animate__animated animate__fadeIn" [formGroup]="form">
    <div class="d-flex align-items-start flex-wrap w-100 my-6" [formGroup]="RuleTargetForm">
      <label class="required col-form-label col-12 col-lg-5">Aturan Target</label>
      <div class="col-12 col-lg-7">
        <div class="form-check text-capitalize p-0 ms-n5">
          <app-input-select-radio
            [disable]="isDisableEdit()"
            [useLabel]="false"
            [useColumn]="true"
            [options]="targetRuleOptions"
            [formControlName]="'is_multilevel_target'"
            ngDefaultControl
          />
        </div>
        <div *ngIf="isMultiLevelTarget()" class="my-4 px-8">
          <fieldset [disabled]="isDisableEdit()">
            <app-input-select-material
              [placeholder]="'Silahkan pilih jumlah tingkatan'"
              [optionsData]="optionsLevelTargetSubject"
              [useLabel]="false"
              [useFilterList]="false"
              [readOnly]="isDisableEdit()"
              formControlName="enum_level"
              ngDefaultControl
            />
          </fieldset>
        </div>
      </div>
    </div>

    <div class="d-flex align-items-start flex-wrap w-100 my-6 animate__animated animate__fadeIn">
      <label class="required col-form-label col-12 col-lg-5">Tipe Target</label>
      <div class="col-12 col-lg-7">
        <div class="form-check text-capitalize p-0 ms-n5">
          <app-input-select-radio
            [disable]="isDisableEdit()"
            [useLabel]="false"
            [useColumn]="true"
            [options]="targetTypeOptions"
            [formControlName]="'type_target'"
            ngDefaultControl
          />
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #loaderTpl>
  <div><app-section-loader /></div>
</ng-template>
