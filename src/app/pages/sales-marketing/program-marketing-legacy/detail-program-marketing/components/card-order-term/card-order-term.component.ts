import { AfterViewInit, Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { IProgramMarketingDetailLegacy, ILegacyProgramMarketingDetail__OrderTerm } from '../../../program-marketing-legacy.interface';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UtilitiesService } from '@services/utilities.service';
import { EnumProgramMarketingPOType, EnumProgramMarketingPOTypeString, EnumProgramMarketingSection, EnumVerificationStatus } from '../../../program-marketing.enum';
import { ActivatedRoute } from '@angular/router';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';

@Component({
  selector: 'app-card-order-term',
  templateUrl: './card-order-term.component.html',
  styleUrls: ['./card-order-term.component.scss'],
})
export class CardOrderTermComponent implements OnInit, AfterViewInit, OnDestroy {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  promagID!: string;
  detailProgramMarketing$!: Observable<IProgramMarketingDetailLegacy>;
  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @Output() termNoteChanged = new EventEmitter<string>();

  private unsubscribe = <Subscription[]>[];

  constructor(private programMarketingService: ProgramMarketingLegacyService, public utils: UtilitiesService, private activatedRoute: ActivatedRoute) {
    this.promagID = this.activatedRoute.snapshot.params.id;
    this.detailProgramMarketing$ = this.programMarketingService.detailProgramMarketing$;
  }

  ngOnInit(): void {
    this.subscriptionDetail();
  }

  subscriptionDetail() {
    this.isLoadingSubject.next(true);
    const _detailSubs = this.detailProgramMarketing$.subscribe((data) => {
      if (data) {
        this.isLoadingSubject.next(false);
        this.handleSessionInput();
      }
    });

    this.unsubscribe.push(_detailSubs);
  }

  updateVerification(status: EnumVerificationStatus | null) {
    const _payload = {
      type: EnumProgramMarketingSection.ORDER_TERM,
      status: status,
    };

    this.isLoadingSubject.next(true);
    this.programMarketingService.updateSectionVerification(this.promagID, _payload).subscribe((resp) => {
      if (resp && resp.success) this.fetchUpdatedDetailData();
    });
  }

  fetchUpdatedDetailData() {
    this.programMarketingService.getProgramMarketingDetail(this.promagID).subscribe((resp) => {
      if (resp) this.programMarketingService.DetailProgramMarketing = resp;
    });
  }

  renderPOProduct = (type: EnumProgramMarketingPOType) => this.programMarketingService.renderPurchaseOrderProducts(type);

  renderMinimumBuy(orderTerm: ILegacyProgramMarketingDetail__OrderTerm) {
    let val = '-';

    const { accumulation } = orderTerm;
    Object.keys(accumulation).forEach((item) => {
      const k = item as keyof typeof accumulation;
      if (accumulation[k] || accumulation[k] === 0) {
        const variant = accumulation.variants[0];
        if (item === 'minimum_price') val = this.utils.toRupiah(Number(accumulation[k]));
        else if (item === 'minimum_qty') val = `${this.utils.toThousandConvert(Number(accumulation[k]))} ${variant.sale_unit}`;
        else val = `${this.utils.toThousandConvert(Number(accumulation[k]))} ${variant.delivery_unit}`;
      }
    });

    this.programMarketingService.MinimumBuy = val;
    return val;
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) return (this.showInputRevisionNote = false);
    return this.toggleRevisionNote();
  }

  isStatusSubmitted = () => this.programMarketingService.isProgramStatusSubmitted;

  isSectionVerified() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.order_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionVerified(_statusEnum);
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.order_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionRequestRevision(_statusEnum);
  }

  getVerificationStatus = () => this.programMarketingService.DetailProgramMarketing.order_term.status_verified_enum as EnumVerificationStatus;

  toggleRevisionNote = () => (this.showInputRevisionNote = !this.showInputRevisionNote);

  showVerifiedBadge = () => this.isStatusSubmitted() && this.programMarketingService.DetailProgramMarketing.order_term.status_verified_enum !== null;

  onNoteChanged = (e: string) => this.termNoteChanged.emit(e?.trim());

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    const _sessionValue = this.programMarketingService.getSessionRevisionNote()?.revision_note;
    if (!!(_sessionValue && !!Object.keys(_sessionValue).length)) setTimeout(() => this.inputRevision.noteControl.setValue(_sessionValue.order_term));
  }

  ngAfterViewInit(): void {
    this.handleSessionInput();
  }

  showBtnGroup(type: EnumVerificationStatus) {
    if (type === EnumVerificationStatus.VERIFIED) return this.programMarketingService.getPrivilegeCTAVerifyProgram();
    return this.programMarketingService.getPrivilegeCTARequestRevision();
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumProgramMarketingPOTypeString = EnumProgramMarketingPOTypeString;
  protected readonly EnumProgramMarketingPOType = EnumProgramMarketingPOType;
  protected readonly EnumVerificationStatus = EnumVerificationStatus;
}
