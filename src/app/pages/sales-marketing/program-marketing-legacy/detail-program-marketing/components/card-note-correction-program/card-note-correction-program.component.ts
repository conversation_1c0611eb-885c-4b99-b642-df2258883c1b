import { Component, OnInit } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy.service';
import { IProgramMarketingDetailLegacy } from '../../../program-marketing-legacy.interface';
import { Observable, of } from 'rxjs';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EnumProgramMarketingType } from '../../../program-marketing.enum';

@Component({
  selector: 'app-card-note-correction-program',
  templateUrl: './card-note-correction-program.component.html',
  styleUrls: ['./card-note-correction-program.component.scss'],
})
export class CardNoteCorrectionProgramComponent implements OnInit {
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;

  detailProgram$!: Observable<IProgramMarketingDetailLegacy>;
  promagID!: string;

  constructor(
    private programMarketingService: ProgramMarketingLegacyService,
    private rolePrivilegeService: RolePrivilegeService,
    private router: Router,
    private activeRoute: ActivatedRoute
  ) {
    this.promagID = this.activeRoute.snapshot.params.id;
  }

  ngOnInit(): void {
    this.detailProgram$ = of(this.programMarketingService.DetailProgramMarketing);
    this.hasPrivilegeEdit();
  }

  hasPrivilegeEdit() {
    return this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_EDIT_PROGRAM');
  }

  goToEdit() {
    const type = this.programMarketingService.DetailProgramMarketing.information.program_type_enum;
    const _type = (type as unknown as string) === 'ONE_SHOT' ? EnumProgramMarketingType.ONE_SHOOT : type;
    return this.router.navigate(['/sales-marketing/program-marketing/form/' + this.promagID], {
      queryParams: {
        type: _type,
      },
    });
  }
}
