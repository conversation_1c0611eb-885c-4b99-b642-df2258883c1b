import { AfterViewInit, Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { DiscountTerm__DiscountProduct, IProgramMarketingDetailLegacy } from '../../../program-marketing-legacy.interface';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy.service';
import { UtilitiesService } from '@services/utilities.service';
import { ActivatedRoute } from '@angular/router';
import {
  EnumProgramMarketingDiscountCategory,
  EnumProgramMarketingDiscountCategoryString,
  EnumProgramMarketingDiscountProgramTypeString,
  EnumProgramMarketingDiscountScopeString,
  EnumProgramMarketingDiscountType,
  EnumProgramMarketingDiscountTypeString,
  EnumProgramMarketingSection,
  EnumProgramMarketingType,
  EnumVerificationStatus,
} from '../../../program-marketing.enum';
import { TableColumn } from '@shared/interface/table.interface';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-card-discount-term',
  templateUrl: './card-discount-term.component.html',
  styleUrls: ['./card-discount-term.component.scss'],
})
export class CardDiscountTermComponent implements OnInit, AfterViewInit, OnDestroy {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  promagID!: string;
  req: boolean = false;
  confirm: boolean = false;

  detailProgramMarketing$!: Observable<IProgramMarketingDetailLegacy>;
  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @Output() termNoteChanged = new EventEmitter<string>();
  @Output() dataDiscountProducts = new EventEmitter<{
    tableColumns: TableColumn[];
    data: DiscountTerm__DiscountProduct[];
  }>();
  @Output() dataDiscountPurchase = new EventEmitter<string>();

  tableColumns!: TableColumn[];
  tableDiscountProducts!: DiscountTerm__DiscountProduct[];
  displayedColumns!: string[];

  private unsubscribe = <Subscription[]>[];

  constructor(
    private programMarketingService: ProgramMarketingLegacyService,
    public utils: UtilitiesService,
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer
  ) {
    this.promagID = this.activatedRoute.snapshot.params.id;
    this.detailProgramMarketing$ = this.programMarketingService.detailProgramMarketing$;
  }

  ngOnInit(): void {
    this.subscriptionDetail();
    this.initDiscountProductTable();
  }

  subscriptionDetail() {
    this.isLoadingSubject.next(true);
    const _detailSubs = this.detailProgramMarketing$.subscribe((data) => {
      if (data) {
        this.isLoadingSubject.next(false);
        this.handleSessionInput();
      }
    });

    this.unsubscribe.push(_detailSubs);
  }

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    const _sessionValue = this.programMarketingService.getSessionRevisionNote()?.revision_note;
    if (!!_sessionValue && !!Object.keys(_sessionValue).length) setTimeout(() => this.inputRevision.noteControl.setValue(_sessionValue.discount_term));
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) return (this.showInputRevisionNote = false);
    return this.toggleRevisionNote();
  }

  updateVerification(status: EnumVerificationStatus | null) {
    this.isLoadingSubject.next(true);
    const _payload = {
      type: EnumProgramMarketingSection.DISCOUNT_TERM,
      status,
    };
    this.programMarketingService.updateSectionVerification(this.promagID, _payload).subscribe((resp) => {
      if (resp && resp.success) this.fetchUpdateDetailData();
    });
  }

  fetchUpdateDetailData() {
    this.programMarketingService.getProgramMarketingDetail(this.promagID).subscribe((resp) => {
      if (resp) this.programMarketingService.DetailProgramMarketing = resp;
      this.isLoadingSubject.next(false);
    });
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.discount_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionRequestRevision(_statusEnum);
  }

  isSectionVerified() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.discount_term.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionVerified(_statusEnum);
  }

  showBtnGroup(type: EnumVerificationStatus) {
    if (type === EnumVerificationStatus.VERIFIED) return this.programMarketingService.getPrivilegeCTAVerifyProgram();
    return this.programMarketingService.getPrivilegeCTARequestRevision();
  }

  getProgramType = () => this.programMarketingService.ProgramMarketingType;

  isStatusSubmitted = () => this.programMarketingService.isProgramStatusSubmitted;

  getVerificationStatus = () => this.programMarketingService.DetailProgramMarketing.discount_term.status_verified_enum as EnumVerificationStatus;

  toggleRevisionNote = () => (this.showInputRevisionNote = !this.showInputRevisionNote);

  showVerifiedBadge = () => this.isStatusSubmitted() && this.programMarketingService.DetailProgramMarketing.discount_term.status_verified_enum !== null;

  onNoteChanged = (e: string) => this.termNoteChanged.emit(e?.trim());

  initDiscountProductTable() {
    const _type = this.getProgramType();
    if (_type !== EnumProgramMarketingType.DISCOUNT_PRODUCT) return;

    const { discount_type_enum, discount_category, discount_products } = this.programMarketingService.DiscountTerm;
    discount_products.sort((a, b) => a.variant_name.localeCompare(b.variant_name, undefined, { sensitivity: 'base' }));
    this.tableDiscountProducts = discount_products;

    const renderDiscountKey = () => {
      switch (discount_category) {
        case EnumProgramMarketingDiscountCategory.PERCENTAGE:
          return discount_type_enum === EnumProgramMarketingDiscountType.SET_MAXIMUM_SALES_DISCOUNT ? 'Maksimal Diskon' : 'Diskon';
        default:
          return 'Nominal';
      }
    };

    this.tableColumns = [
      { key: 'variant_name', title: 'PRODUK', isSortable: false },
      { key: 'discount', title: renderDiscountKey(), isSortable: false },
      { key: 'minimum_order', title: 'MINIMAL PEMBELIAN', isSortable: false },
    ];

    this.displayedColumns = this.tableColumns.map((col) => col.key);
    this.dataDiscountProducts.emit({ tableColumns: this.tableColumns, data: this.tableDiscountProducts });
  }

  discountPurchaseProducts(data: string[]) {
    if (!data || (data && !data.length)) return '-';
    const _products = data.map((item) => `<span>${item}</span>`).join('');
    this.dataDiscountPurchase.emit(_products);
    return this.sanitizer.bypassSecurityTrustHtml(_products);
  }

  ngAfterViewInit(): void {}

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly EnumProgramMarketingDiscountScopeString = EnumProgramMarketingDiscountScopeString;
  protected readonly EnumProgramMarketingDiscountProgramTypeString = EnumProgramMarketingDiscountProgramTypeString;
  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumProgramMarketingDiscountCategoryString = EnumProgramMarketingDiscountCategoryString;
  protected readonly EnumProgramMarketingDiscountTypeString = EnumProgramMarketingDiscountTypeString;
}
