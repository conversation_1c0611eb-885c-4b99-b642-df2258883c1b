<app-card *ngIf="isLoadingSubject | async; else cardDataTpl" [cardClasses]="'mb-8 animation animation-fade-in'">
  <ng-container cardBody>
    <app-section-loader />
  </ng-container>
</app-card>

<ng-template #cardDataTpl>
  <ng-container *ngIf="(detailProgramMarketing$ | async)?.program_term as programTerm">
    <app-card
      [cardBodyClasses]="'pt-0'"
      [cardClasses]="(isSectionVerified() ? 'border border-primary' : isSectionRequestRevision() ? 'border border-danger' : '') + ' mb-8 animation animation-fade-in'"
      [header]="true"
    >
      <ng-container cardHeader>
        <h5 class="fw-bolder d-flex w-100 align-items-center mb-0">
          <span>Ketentuan Program</span>
          <app-section-verified-badge *ngIf="showVerifiedBadge()" [isRequestRevision]="isSectionRequestRevision()" [sectionVerificationStatus]="getVerificationStatus()" />
        </h5>
      </ng-container>
      <ng-container cardBody>
        <div class="row">
          <!-- cakupan -->
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Cakupan</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ programTerm.scope_string }}</span>
          </div>

          <!-- kuota -->
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Kuota</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <div class="d-flex flex-column">
              <!--      todo: need enhance render quota - use updated interface (IProgramMarketingDetail__ProgramTerm)-->
<!--              <ng-container *ngFor="let quota of programTerm.quota; let f = first">-->
<!--                <div *ngIf="f; else defaultQuota">: {{ quota }}</div>-->
<!--                <ng-template #defaultQuota>-->
<!--                  <div class="ms-2">{{ quota }}</div>-->
<!--                </ng-template>-->
<!--              </ng-container>-->
            </div>
          </div>

          <!-- revision note -->
          <app-input-revision-note #inputRevision (noteChanged)="onNoteChanged($event)" *ngIf="isSectionRequestRevision()" />

          <!--
            CTA: verify, request revision, reset verification
            show only on status submitted - for admin/finance
          -->
          <app-button-group-verify
            *ngIf="isStatusSubmitted()"
            [sectionStatusVerification]="getVerificationStatus()"
            [showBtnVerify]="showBtnGroup(EnumVerificationStatus.VERIFIED)"
            [showBtnRequestRevision]="showBtnGroup(EnumVerificationStatus.REQUEST_REVISION)"
            (ctaVerification)="handleActionVerification($event)"
          />
        </div>
      </ng-container>
    </app-card>
  </ng-container>
</ng-template>
