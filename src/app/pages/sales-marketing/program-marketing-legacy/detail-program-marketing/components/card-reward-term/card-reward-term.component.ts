import { AfterViewInit, Component, <PERSON>Emitter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { IProgramMarketingDetailLegacy } from '../../../program-marketing-legacy.interface';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';
import { EnumProgramMarketingRewardType, EnumProgramMarketingSection, EnumVerificationStatus } from '../../../program-marketing.enum';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy.service';
import { UtilitiesService } from '@services/utilities.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-card-reward-term',
  templateUrl: './card-reward-term.component.html',
  styleUrls: ['./card-reward-term.component.scss'],
})
export class CardRewardTermComponent implements OnInit, AfterViewInit, OnDestroy {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  promagID!: string;

  detailProgramMarketing$!: Observable<IProgramMarketingDetailLegacy>;
  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @Output() termNoteChanged = new EventEmitter<string>();

  private unsubscribe = <Subscription[]>[];

  constructor(private programMarketingService: ProgramMarketingLegacyService, public utils: UtilitiesService, private activatedRoute: ActivatedRoute) {
    this.promagID = this.activatedRoute.snapshot.params.id;
    this.detailProgramMarketing$ = this.programMarketingService.detailProgramMarketing$;
  }

  ngOnInit() {}

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    const _sessionValue = this.programMarketingService.getSessionRevisionNote()?.revision_note;
    if (!!_sessionValue && !!Object.keys(_sessionValue).length) setTimeout(() => this.inputRevision.noteControl.setValue(_sessionValue.reward));
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) return (this.showInputRevisionNote = false);
    return this.toggleRevisionNote();
  }

  updateVerification(status: EnumVerificationStatus | null) {
    this.isLoadingSubject.next(true);

    const _payload = {
      type: EnumProgramMarketingSection.REWARD,
      status,
    };

    this.programMarketingService.updateSectionVerification(this.promagID, _payload).subscribe((resp) => {
      if (resp && resp.success) this.fetchUpdateDetailData();
    });
  }

  fetchUpdateDetailData() {
    this.programMarketingService.getProgramMarketingDetail(this.promagID).subscribe((resp) => {
      if (resp) this.programMarketingService.DetailProgramMarketing = resp;
      this.isLoadingSubject.next(false);
    });
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.reward.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionRequestRevision(_statusEnum);
  }

  isSectionVerified() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.reward.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionVerified(_statusEnum);
  }

  showBtnGroup(type: EnumVerificationStatus) {
    if (type === EnumVerificationStatus.VERIFIED) return this.programMarketingService.getPrivilegeCTAVerifyProgram();
    return this.programMarketingService.getPrivilegeCTARequestRevision();
  }

  isStatusSubmitted = () => this.programMarketingService.isProgramStatusSubmitted;

  getVerificationStatus = () => this.programMarketingService.DetailProgramMarketing.reward.status_verified_enum as EnumVerificationStatus;

  toggleRevisionNote = () => (this.showInputRevisionNote = !this.showInputRevisionNote);

  showVerifiedBadge = () => this.isStatusSubmitted() && this.programMarketingService.DetailProgramMarketing.reward.status_verified_enum !== null;

  onNoteChanged = (e: string) => this.termNoteChanged.emit(e?.trim());

  renderIstMultiplicationType = (val: string | boolean) => {
    if (typeof val === 'boolean') {
      return val ? 'Berlaku Kelipatan' : 'Tidak Berlaku Kelipatan';
    } else return val;
  };

  ngAfterViewInit(): void {
    this.handleSessionInput();
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumProgramMarketingRewardType = EnumProgramMarketingRewardType;
}
