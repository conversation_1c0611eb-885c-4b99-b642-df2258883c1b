<app-card *ngIf="isLoadingSubject | async; else cardDataTpl" [cardClasses]="'mb-8 animation animation-fade-in'">
  <ng-container cardBody><app-section-loader /></ng-container>
</app-card>

<ng-template #cardDataTpl>
  <ng-container *ngIf="(detailProgramMarketing$ | async)?.reward as rewardTerm">
    <app-card
      [cardClasses]="(isSectionVerified() ? 'border border-primary' : isSectionRequestRevision() ? 'border border-danger' : '') + ' mb-8 animation animation-fade-in'"
      [header]="true"
      [cardBodyClasses]="'pt-0'"
    >
      <ng-container cardHeader>
        <h5 class="fw-bolder d-flex w-100 align-items-center">
          <span>Ketentuan <PERSON></span>
          <app-section-verified-badge *ngIf="showVerifiedBadge()" [sectionVerificationStatus]="getVerificationStatus()" [isRequestRevision]="isSectionRequestRevision()" />
        </h5>
      </ng-container>
      <ng-container cardBody>
        <!-- aturan hadiah -->
        <div class="row">
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Aturan Hadiah</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ renderIstMultiplicationType(rewardTerm.multiplication_type) }}</span>
          </div>
        </div>

        <!-- produk hadiah -->
        <div class="row">
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Produk Hadiah</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>
              <ng-container [ngTemplateOutlet]="rewardTerm.reward_type_enum === EnumProgramMarketingRewardType.MAI_PRODUCT ? maiProductTpl : nonMaiProductTpl"></ng-container>
            </span>
            <ng-template #maiProductTpl>
              {{ rewardTerm.mai_product?.name }} ({{ rewardTerm.mai_product?.qty }} <span class="text-capitalize">{{ rewardTerm.mai_product?.unit }})</span>
            </ng-template>
            <ng-template #nonMaiProductTpl>{{ rewardTerm.non_mai_product?.product_other_reward }}</ng-template>
          </div>
        </div>

        <!-- maksimal budget -->
        <div class="row" *ngIf="rewardTerm.reward_type_enum === EnumProgramMarketingRewardType.NON_MAI_PRODUCT">
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Maksimal Budget</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ utils.toRupiah(rewardTerm.non_mai_product?.maximum_budget_other_reward ?? 0) }}</span>
          </div>
        </div>

        <!-- revision note -->
        <app-input-revision-note #inputRevision *ngIf="isSectionRequestRevision()" (noteChanged)="onNoteChanged($event)" />

        <!--
          CTA: verify, request revision, reset verification
          show only on status submitted - for admin
        -->
        <app-button-group-verify
          *ngIf="isStatusSubmitted()"
          [sectionStatusVerification]="getVerificationStatus()"
          [showBtnVerify]="showBtnGroup(EnumVerificationStatus.VERIFIED)"
          [showBtnRequestRevision]="showBtnGroup(EnumVerificationStatus.REQUEST_REVISION)"
          (ctaVerification)="handleActionVerification($event)"
        />
      </ng-container>
    </app-card>
  </ng-container>
</ng-template>
