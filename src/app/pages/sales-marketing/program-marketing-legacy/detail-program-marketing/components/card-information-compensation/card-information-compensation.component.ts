import { Component, EventEmitter, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { IProgramMarketingDetailLegacy } from '../../../program-marketing-legacy.interface';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy.service';
import { UtilitiesService } from '@services/utilities.service';
import { ActivatedRoute } from '@angular/router';
import { EnumProgramMarketingDiscountCategory, EnumProgramMarketingSection, EnumVerificationStatus } from '../../../program-marketing.enum';
import { BaseDatasource } from '@shared/base/base.datasource';
import { TableColumn } from '@shared/interface/table.interface';

@Component({
  selector: 'app-card-information-compensation',
  templateUrl: './card-information-compensation.component.html',
  styleUrls: ['./card-information-compensation.component.scss'],
})
export class CardInformationCompensationComponent implements OnInit, OnDestroy {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  promagID!: string;
  detailProgramMarketing$!: Observable<IProgramMarketingDetailLegacy>;

  baseDataSourceList: BaseDatasource<any>;
  displayedColumns!: string[];
  tableColumns: TableColumn[] = [
    {
      key: 'name',
      title: 'PRODUK KOMPENSASI',
      isSortable: false,
    },
    {
      key: 'qty',
      title: 'QTY KOMPENSASI',
      isSortable: false,
    },
  ];

  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @Output() termNoteChanged = new EventEmitter<string>();

  private unsubscribe = <Subscription[]>[];

  constructor(private programMarketingService: ProgramMarketingLegacyService, public utils: UtilitiesService, private activatedRoute: ActivatedRoute) {
    this.promagID = this.activatedRoute.snapshot.params.id;
    this.detailProgramMarketing$ = this.programMarketingService.detailProgramMarketing$;
  }

  ngOnInit(): void {
    this.subscriptionDetail();
  }

  subscriptionDetail() {
    this.isLoadingSubject.next(true);
    const _detailSubs = this.detailProgramMarketing$.subscribe((data) => {
      if (data) {
        this.isLoadingSubject.next(false);
        this.handleSessionInput();
      }
    });

    this.unsubscribe.push(_detailSubs);
  }

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    const _sessionValue = this.programMarketingService.getSessionRevisionNote()?.revision_note;
    if (!!_sessionValue && !!Object.keys(_sessionValue).length) setTimeout(() => this.inputRevision.noteControl.setValue(_sessionValue.discount_term));
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.compensation.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionRequestRevision(_statusEnum);
  }

  isSectionVerified() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.compensation.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionVerified(_statusEnum);
  }

  showBtnGroup(type: EnumVerificationStatus) {
    if (type === EnumVerificationStatus.VERIFIED) return this.programMarketingService.getPrivilegeCTAVerifyProgram();
    return this.programMarketingService.getPrivilegeCTARequestRevision();
  }

  isStatusSubmitted = () => this.programMarketingService.isProgramStatusSubmitted;

  getVerificationStatus = () => this.programMarketingService.DetailProgramMarketing.compensation.status_verified_enum as EnumVerificationStatus;

  toggleRevisionNote = () => (this.showInputRevisionNote = !this.showInputRevisionNote);

  showVerifiedBadge = () => this.isStatusSubmitted() && this.programMarketingService.DetailProgramMarketing.compensation.status_verified_enum !== null;

  onNoteChanged = (e: string) => this.termNoteChanged.emit(e?.trim());

  getDataSource(product: any[]) {
    this.baseDataSourceList = product as unknown as BaseDatasource<any>;
    this.baseDataSourceList.hasItems = false;
    this.displayedColumns = this.tableColumns.map((item) => item.key);
    return this.baseDataSourceList;
  }

  updateVerification(status: EnumVerificationStatus | null) {
    const _payload = {
      type: EnumProgramMarketingSection.COMPENSATION,
      status: status,
    };

    this.isLoadingSubject.next(true);
    this.programMarketingService.updateSectionVerification(this.promagID, _payload).subscribe((resp) => {
      if (resp && resp.success) this.fetchUpdatedDetailData();
    });
  }

  fetchUpdatedDetailData() {
    this.programMarketingService.getProgramMarketingDetail(this.promagID).subscribe((resp) => {
      if (resp) this.programMarketingService.DetailProgramMarketing = resp;
    });
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) return (this.showInputRevisionNote = false);
    return this.toggleRevisionNote();
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly EnumProgramMarketingDiscountCategory = EnumProgramMarketingDiscountCategory;
}
