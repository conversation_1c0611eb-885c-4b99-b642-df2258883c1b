<app-card *ngIf="isLoadingSubject | async; else cardDataTpl" [cardClasses]="'mb-8 animation animation-fade-in'">
  <ng-container cardBody>
    <app-section-loader />
  </ng-container>
</app-card>

<ng-template #cardDataTpl>
  <ng-container *ngIf="(detailProgramMarketing$ | async)?.information as information">
    <app-card
      [cardBodyClasses]="'pt-0'"
      [cardClasses]="(isSectionVerified() ? 'border border-primary' : isSectionRequestRevision() ? 'border border-danger' : '') + ' mb-8 animation animation-fade-in'"
      [header]="true"
    >
      <ng-container cardHeader>
        <h6 class="fw-bolder d-flex w-100 align-items-center mb-0">
          <span>Informasi Program Marketing</span>
          <app-section-verified-badge *ngIf="showVerifiedBadge()" [isRequestRevision]="isSectionRequestRevision()" [sectionVerificationStatus]="getVerificationStatus()" />
        </h6>
      </ng-container>
      <ng-container cardBody>
        <div class="row">
          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Tipe Program</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ information.program_type_string }}</span>
          </div>

          <ng-container *ngIf="getUsePeriod(information.program_type_enum)">
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Periode</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <span class="me-1">:</span>
              <span>{{ utils.formatEpochToDate(information.period_start) }} s/d {{ utils.formatEpochToDate(information.period_end) }}</span>
              <div class="text-info mt-3" *ngIf="information.extend_period_end">
                <span class="me-1">:</span>
                <span>{{ utils.formatEpochToDate(information.extend_period_end) }}</span>
              </div>
            </div>
          </ng-container>

          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Catatan Management</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ information.management_note ? information.management_note : '-' }}</span>
          </div>

          <div class="col-12 col-md-4 mb-2">
            <span class="label text-gray-700">Nomor Surat Pengajuan</span>
          </div>
          <div class="col-12 col-md-8 mb-4">
            <span class="me-1">:</span>
            <span>{{ information.reference_number ? information.reference_number : '-' }}</span>
          </div>

          <ng-container *ngIf="!isStatusSubmitted()">
            <div class="col-12 col-md-4 mb-2">
              <span class="label text-gray-700">Dokumen</span>
            </div>
            <div class="col-12 col-md-8 mb-4">
              <div class="max-width-document">
                <app-document-preview [data]="getDocumentUrl()" />
              </div>
            </div>
          </ng-container>
        </div>

        <!-- revision note -->
        <app-input-revision-note #inputRevision (noteChanged)="onNoteChanged($event)" *ngIf="isSectionRequestRevision()" />

        <!--
          CTA: verify, request revision, reset verification
          show only on status submitted - for admin/finance
        -->
        <app-button-group-verify
          *ngIf="isStatusSubmitted()"
          [sectionStatusVerification]="getVerificationStatus()"
          [showBtnVerify]="showBtnGroup(EnumVerificationStatus.VERIFIED)"
          [showBtnRequestRevision]="showBtnGroup(EnumVerificationStatus.REQUEST_REVISION)"
          (ctaVerification)="handleActionVerification($event)"
        />
      </ng-container>
    </app-card>
  </ng-container>
</ng-template>
