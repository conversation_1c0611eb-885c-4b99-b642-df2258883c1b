import { AfterViewInit, Component, <PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { ProgramMarketingLegacyService } from '../../../program-marketing-legacy.service';
import { IProgramMarketingDetailLegacy } from '../../../program-marketing-legacy.interface';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { EnumProgramMarketingSection, EnumProgramMarketingType, EnumVerificationStatus } from '../../../program-marketing.enum';
import { ActivatedRoute } from '@angular/router';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';

@Component({
  selector: 'app-card-information-program',
  templateUrl: './card-information-program.component.html',
  styleUrls: ['./card-information-program.component.scss'],
})
export class CardInformationProgramComponent implements OnInit, AfterViewInit, OnDestroy {
  isLoadingSubject = new BehaviorSubject(false);
  showInputRevisionNote = false;
  promagID!: string;

  detailProgramMarketing$: Observable<IProgramMarketingDetailLegacy | null>;
  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @Output() termNoteChanged = new EventEmitter<string>();
  private unsubscribe = <Subscription[]>[];

  constructor(private programMarketingService: ProgramMarketingLegacyService, public utils: UtilitiesService, private activatedRoute: ActivatedRoute) {
    this.promagID = this.activatedRoute.snapshot.params.id;
    this.detailProgramMarketing$ = this.programMarketingService.detailProgramMarketing$;
  }

  ngOnInit(): void {
    this.subscriptionDetail();
  }

  subscriptionDetail() {
    this.isLoadingSubject.next(true);
    const _detailSubs = this.detailProgramMarketing$.subscribe((data) => {
      if (data) {
        this.isLoadingSubject.next(false);
        this.handleSessionInput();
      }
    });

    this.unsubscribe.push(_detailSubs);
  }

  updateVerification(status: EnumVerificationStatus | null) {
    const _payload = {
      type: EnumProgramMarketingSection.INFORMATION,
      status: status,
    };

    this.isLoadingSubject.next(true);
    this.programMarketingService.updateSectionVerification(this.promagID, _payload).subscribe((resp) => {
      if (resp && resp.success) this.fetchUpdatedDetailData();
    });
  }

  fetchUpdatedDetailData() {
    this.programMarketingService.getProgramMarketingDetail(this.promagID).subscribe((resp) => {
      if (resp) this.programMarketingService.DetailProgramMarketing = resp;
    });
  }

  isStatusSubmitted = () => this.programMarketingService.isProgramStatusSubmitted;

  isSectionVerified() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.information.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionVerified(_statusEnum);
  }

  isSectionRequestRevision() {
    const _statusEnum = this.programMarketingService.DetailProgramMarketing.information.status_verified_enum;
    return this.isStatusSubmitted() && this.programMarketingService.isSectionRequestRevision(_statusEnum);
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) return (this.showInputRevisionNote = false);
    return this.toggleRevisionNote();
  }

  toggleRevisionNote = () => (this.showInputRevisionNote = !this.showInputRevisionNote);

  getDocumentUrl = () => this.programMarketingService.programDocumentUrl;

  getVerificationStatus = () => this.programMarketingService.DetailProgramMarketing.information.status_verified_enum as EnumVerificationStatus;

  showVerifiedBadge = () => this.isStatusSubmitted() && this.programMarketingService.DetailProgramMarketing.information.status_verified_enum !== null;

  onNoteChanged = (e: string) => this.termNoteChanged.emit(e?.trim());

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    const _sessionValue = this.programMarketingService.getSessionRevisionNote()?.revision_note;
    if (!!(_sessionValue && !!Object.keys(_sessionValue).length)) setTimeout(() => this.inputRevision.noteControl.setValue(_sessionValue.information));
  }

  ngAfterViewInit() {
    this.handleSessionInput();
  }

  getUsePeriod(type: EnumProgramMarketingType) {
    return type !== EnumProgramMarketingType.PRODUCT_COMPENSATION;
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  showBtnGroup(type: EnumVerificationStatus) {
    if (type === EnumVerificationStatus.VERIFIED) return this.programMarketingService.getPrivilegeCTAVerifyProgram();
    return this.programMarketingService.getPrivilegeCTARequestRevision();
  }

  protected readonly EnumVerificationStatus = EnumVerificationStatus;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
