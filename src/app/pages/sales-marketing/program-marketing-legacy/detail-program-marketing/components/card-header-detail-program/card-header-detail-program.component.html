<app-card [header]="true" [cardClasses]="'mb-8'" [cardBodyClasses]="'pt-0'" [cardHeaderClasses]="'align-items-center'">
  <ng-container cardHeader>
    <div class="w-100 border-bottom border-gray-300 pb-5">
      <div class="card-title">
        <h4 class="fw-bold m-0">{{ data.name }}</h4>
        <span class="badge badge__status badge__status--{{ data.status_program_marketing_enum }} ms-auto">{{ data.status_program_marketing_string }}</span>
      </div>
      <div class="d-flex align-items-center fs-8 mt-3" *ngIf="!!data.modified">
        <span class="me-2 svg-icon svg-icon-24" [inlineSVG]="STRING_CONSTANTS.ICON.IC_TIME_CLOCK_LINE"></span>
        <span class="text-gray-700">Program telah diedit Admin Marketing pada {{ utils.formatEpochToDate(data.modified.date) }}</span>
      </div>
    </div>
  </ng-container>

  <ng-container cardBody>
    <div class="row">
      <!-- created -->
      <div class="col-12 col-md-6">
        <div class="text-gray-700">
          <span class="label">Tanggal Dibuat</span>
        </div>
        <div class="fw-bolder mt-4">
          <span class="d-block">{{ data.created.actor_name }}</span>
          <span class="d-block mt-2">{{ utils.formatEpochToDate(data.created.date) }}, {{ utils.formatEpochToDate(data.created.date, 'HH:mm:ss') }}</span>
        </div>
      </div>

      <!-- approved -->
      <div class="col-12 col-md-6">
        <div class="text-gray-700">
          <span class="label">Tanggal Approve Finance</span>
        </div>
        <div class="fw-bolder mt-4">
          <ng-container *ngIf="data.finance_approved; else noApproved">
            <span class="d-block">{{ data.finance_approved.actor_name }}</span>
            <span class="d-block mt-2">{{ utils.formatEpochToDate(data.finance_approved.date) }} , {{ utils.formatEpochToDate(data.finance_approved.date, 'HH:mm:ss') }}</span>
          </ng-container>
          <ng-template #noApproved><span class="d-block">-</span></ng-template>
        </div>
      </div>
    </div>
  </ng-container>
</app-card>
