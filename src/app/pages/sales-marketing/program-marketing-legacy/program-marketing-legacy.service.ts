import { Injectable } from '@angular/core';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import {
  CounterList,
  IDetailListPO,
  IDistributorOption,
  IPayloadProgramVerification,
  IProductGroupBrand,
  IProductRewardProgramMarketing,
  IProgramMarketingDetailLegacy,
} from './program-marketing-legacy.interface';
import { map } from 'rxjs/operators';
import { ICardHeaderCounter } from '@shared/components/card/card-header/card-header.model';
import { PageLink } from '@metronic/layout';
import { BehaviorSubject, shareReplay, tap } from 'rxjs';
import {
  EnumProgramMarketingDiscountProgramType,
  EnumProgramMarketingPOType,
  EnumProgramMarketingRewardType,
  EnumProgramMarketingSection,
  EnumProgramMarketingStatus,
  EnumVerificationStatus,
} from './program-marketing.enum';
import { purchaseOrderTableColumns } from '../../purchase-order/purchase-order.data';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { ProgramMarketingDetailModel } from '../program-marketing/program-marketing-detail.model';
import { IProductOrder__ProductList } from '@shared/interface/product-order.interface';
import { CreateSpmModel } from '../../sales-order/v1/create-spm/create-spm.model';
import { IPostCreateSPM, ISPM_ResponseData } from '../../spm/spm.interface';
import { IPayloadProductQty } from '@models/product-order.model';
import { ICardProductPromag } from '@shared/components/card/card-product-promag/card-product-promag.interface';
import { IGenericResponseSuccess } from '@shared/interface/generic';
import { Router } from '@angular/router';
import { BaseResponse } from '@shared/base/base-response';
import {
  IInputSelectOptions,
  ISelectDistributorProgramMarketing,
  ISelectProductReward,
} from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.interface';
import { IDistributorArea, IDistributorType } from '@pages/distributor/interfaces/distributor.interface';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';

@Injectable({
  providedIn: 'root',
})
export class ProgramMarketingLegacyService {
  private createSpmModel = new CreateSpmModel();

  private tableColumnsPO = purchaseOrderTableColumns;

  private detailProgramMarketingSubject = new BehaviorSubject<IProgramMarketingDetailLegacy>(new ProgramMarketingDetailModel<IProgramMarketingDetailLegacy>().Data);
  detailProgramMarketing$ = this.detailProgramMarketingSubject.asObservable();
  private minimumBuySubject = new BehaviorSubject<string>('');

  private initHeaderCreateSpmSubject = new BehaviorSubject(this.createSpmModel.HeaderData);
  initHeaderCreateSpm$ = this.initHeaderCreateSpmSubject.asObservable();

  private spmProductOrderListSubject = new BehaviorSubject(this.createSpmModel.ProductOrderList);

  private distributorOptionListSubject = new BehaviorSubject<IDistributorOption[]>([]);

  get DetailProgramMarketing() {
    return this.detailProgramMarketingSubject.value;
  }

  set DetailProgramMarketing(data: IProgramMarketingDetailLegacy) {
    this.detailProgramMarketingSubject.next(data);
  }

  get DistributorOptionList() {
    return this.distributorOptionListSubject.value;
  }

  set DistributorOptionList(data: IDistributorOption[]) {
    this.distributorOptionListSubject.next(data);
  }

  get MinimumBuy() {
    return this.minimumBuySubject.value;
  }

  set MinimumBuy(data: string) {
    this.minimumBuySubject.next(data);
  }

  get ProgramMarketingType() {
    return this.DetailProgramMarketing && this.DetailProgramMarketing.information.program_type_enum;
  }

  get DiscountTerm() {
    return this.DetailProgramMarketing && this.DetailProgramMarketing.discount_term;
  }

  get isProgramStatusSubmitted() {
    return this.DetailProgramMarketing.header.status_program_marketing_enum === EnumProgramMarketingStatus.SUBMITTED;
  }

  get isProgramDiscountSetToMaximum() {
    return this.DetailProgramMarketing.program_term.program_marketing_discount_enum === EnumProgramMarketingDiscountProgramType.SET_MAXIMUM_SALES_DISCOUNT;
  }

  get isProgramRewardNonMAI() {
    return this.DetailProgramMarketing.reward.reward_type_enum === EnumProgramMarketingRewardType.NON_MAI_PRODUCT;
  }

  get programDocumentUrl() {
    return this.DetailProgramMarketing.document_url;
  }

  get initProgramMarketingBreadcrumb() {
    return <PageLink[]>[
      {
        title: 'Sales & Marketing',
        path: '',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: 'Program Marketing Distributor',
        path: '/sales-marketing/program-marketing/list',
        isActive: true,
      },
    ];
  }

  // CREATE SPM
  get InitCreateSpm() {
    return this.initHeaderCreateSpmSubject.value;
  }

  set InitCreateSpm(data) {
    this.initHeaderCreateSpmSubject.next(data);
  }

  get SPMProductOrderList() {
    return this.spmProductOrderListSubject.value;
  }

  set SPMProductOrderList(data) {
    this.spmProductOrderListSubject.next(data);
  }

  setSessionRevisionNote(data: IPayloadProgramVerification) {
    sessionStorage.setItem('revisionNote', JSON.stringify(data));
  }

  getSessionRevisionNote(): IPayloadProgramVerification | undefined {
    const value = sessionStorage.getItem('revisionNote');
    if (!value) return undefined;
    return JSON.parse(value);
  }

  clearSessionRevisionNote() {
    sessionStorage.removeItem('revisionNote');
  }

  constructor(private baseService: BaseService, private rolePrivilegeService: RolePrivilegeService, private router: Router) {}

  getProgramMarketingDetail(id: string) {
    // { disableModalPopupError: true }
    return this.baseService.getData<IProgramMarketingDetailLegacy>(API.PROGRAM_MARKETING.DETAIL_PROGRAM + id).pipe(
      map((resp) => resp && resp.data),
      tap((data) => this.detailProgramMarketingSubject.next(data))
    );
  }

  getCounterList(params: string | undefined) {
    let _endpoint = API.PROGRAM_MARKETING.HEADER_COUNTER_LIST;
    if (params) _endpoint = _endpoint + params;
    return this.baseService.getData<CounterList>(_endpoint, { disableModalPopupError: true }).pipe(
      map((res) => {
        if (res && res.success) {
          return this.counterListMapper(res.data);
        }
      })
    );
  }

  getPurchaseOrderTableColumn() {
    this.tableColumnsPO.map((item) => {
      if (item.key === 'total_box_item') item.title = 'Total QTY';
    });
    return this.tableColumnsPO;
  }

  isSectionVerified = (status?: EnumVerificationStatus | null) => (status ? status === EnumVerificationStatus.VERIFIED : false);

  isSectionRequestRevision = (status?: EnumVerificationStatus | null) => (status ? status === EnumVerificationStatus.REQUEST_REVISION : false);

  isSectionVerifiedOrRequestRevision = (status?: EnumVerificationStatus | null) =>
    status ? status === EnumVerificationStatus.REQUEST_REVISION || status === EnumVerificationStatus.VERIFIED : false;

  updateSectionVerification(
    promagID: string,
    payload: {
      status: EnumVerificationStatus | null;
      type: EnumProgramMarketingSection;
    }
  ) {
    return this.baseService.putData<any>(API.PROGRAM_MARKETING.UPDATE_VERIFICATION_STATUS + promagID, payload);
  }

  postSubmitProgramVerification(promagID: string, payload: IPayloadProgramVerification) {
    return this.baseService.postData<IPayloadProgramVerification>(API.PROGRAM_MARKETING.POST_SUBMIT_VERIFICATION + promagID, payload);
  }

  renderPurchaseOrderProducts(type: EnumProgramMarketingPOType) {
    let items = '';

    if (type === EnumProgramMarketingPOType.BUNDLING) {
      const _variants = this.DetailProgramMarketing.order_term.bundling.variants;
      _variants.forEach((item) => {
        items += `<span class="d-block mb-1">${item.name} (${item.qty} <span class="text-capitalize">${item.sale_unit}</span>)</span>`;
      });
    }

    if (type === EnumProgramMarketingPOType.ACCUMULATION) {
      const _variants = this.DetailProgramMarketing.order_term.accumulation.variants;
      _variants.forEach((item) => {
        items += `<span class="d-block mb-1">${item.name} </span>`;
      });
    }

    return items;
  }

  renderPurchaseOrderRewards(type: EnumProgramMarketingRewardType) {
    const { reward } = this.DetailProgramMarketing;
    if (type === EnumProgramMarketingRewardType.NON_MAI_PRODUCT) return reward.non_mai_product?.product_other_reward;
    else return `${reward.mai_product?.name} (${reward.mai_product?.qty} ${reward.mai_product?.unit})`;
  }

  private counterListMapper(data: CounterList) {
    const keyName = {
      active: 'Aktif',
      scheduled: 'Dijadwalkan',
      finished: 'Selesai',
      submitted: 'Diajukan',
      need_changed: 'Butuh Perbaikan',
    };

    return Object.keys(keyName).map((key) => {
      const k = key as keyof typeof keyName;
      return <ICardHeaderCounter>{
        key: k,
        name: keyName[k],
        value: data[k] ?? 0,
      };
    });
  }

  // Detail promag: section proram - cta privilege checker
  getPrivilegeCTAVerifyProgram(): boolean {
    return this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_VERIFY_PROGRAM');
  }

  getPrivilegeCTARequestRevision(): boolean {
    return this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_REQUEST_REVISION_PROGRAM');
  }

  getPrivilegeCTAConfirmProgram = (): boolean => this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_CONFIRM_PROGRAM');

  getPrivilegeCTAEditProgram = (): boolean => this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_EDIT_PROGRAM');

  getPrivilegeCTAExtendProgram = (): boolean => this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_EDIT_PROGRAM');

  getProductGroup(keyword?: string | null, page: number = 0) {
    const endpoint = API.PROGRAM_MARKETING.GET_PRODUCT_GROUP;
    page = !!keyword ? 0 : page;
    return this.baseService.getData<IProductGroupBrand[]>(endpoint, undefined, { keyword, page });
  }

  getDetailListPO = (paramsId: any) => {
    return this.baseService.getData<IDetailListPO>(API.PROGRAM_MARKETING.GET_COUNT_LIST_PO + paramsId, undefined);
  };

  getRewardProduct = (params?: { page?: number; size?: number; id?: string; keyword?: string }) => {
    return this.baseService.getData<IProductRewardProgramMarketing[]>(API.PROGRAM_MARKETING.GET_REWARD_PRODUCT, undefined, params).pipe(
      map((resp) => {
        const _resp = <BaseResponse<IInputSelectOptions[]>>(<unknown>resp);
        if (resp && resp.data) _resp.data = this.mapInputSelectRewardProduct(resp.data);
        return _resp;
      }),
      shareReplay(1)
    );
  };

  mapInputSelectRewardProduct(data: IProductRewardProgramMarketing[]) {
    return data.map((item) => {
      const inputSelector = new ISelectProductReward();
      inputSelector.setSelector(item);
      return inputSelector as IInputSelectOptions;
    });
  }

  getDistributor = (params?: { page?: number; size?: number; id?: string; keyword?: string }) => {
    return this.baseService.getData<IDistributorOption[]>(API.PROGRAM_MARKETING.GET_DISTRIBUTOR_AREA, undefined, params).pipe(
      map((resp) => {
        const _resp = <BaseResponse<IInputSelectOptions[]>>(<unknown>resp);
        if (resp && resp.data) {
          this.DistributorOptionList = resp.data;
          _resp.data = this.mapInputSelectDistributor(resp.data);
        }
        return _resp;
      }),
      shareReplay(1)
    );
  };

  mapInputSelectDistributor(data: IDistributorOption[]) {
    return data.map((item) => {
      const inputSelector = new ISelectDistributorProgramMarketing();
      inputSelector.setSelector(item);
      return inputSelector as IInputSelectOptions;
    });
  }

  getTypeDoc(doc: string) {
    if (!doc) return undefined;
    const regex = /\bfile\b/;
    return doc.match(regex) ? 'FILE' : 'IMAGE';
  }

  getSpmProductOrderList(id: string, warehouseId: string) {
    const params = `?promotionId=${id}&warehouseId=${warehouseId}`;
    return this.baseService.getData<IProductOrder__ProductList[]>(API.PROGRAM_MARKETING.GET_LIST_PRODUCT_ORDER + params).pipe(
      tap((res) => (this.SPMProductOrderList = res && res.data)),
      map((resp) => resp && resp.data)
    );
  }

  getInitCreateSPM(id: string, warehouseId: string) {
    const params = `?promotion_id=${id}&warehouse_id=${warehouseId}`;
    return this.baseService.getData<ISPM_ResponseData>(API.PROGRAM_MARKETING.GET_HEADER_CREATE_SPM + params).pipe(
      tap((res) => (this.InitCreateSpm = res && res.data)),
      map((resp) => resp && resp.data)
    );
  }

  getPatchProductPromag(id: string, payload: IPayloadProductQty) {
    return this.baseService.patchDataWithBody<ICardProductPromag[]>(API.SALES_ORDER.CREATE_SPM_LIST_PRODUCT_PROGRAM_MARKETING + id, payload).pipe(map((res) => res && res.data));
  }

  postCreateSPM(id: string, payload: IPostCreateSPM) {
    return this.baseService.postData<IGenericResponseSuccess>(API.PROGRAM_MARKETING.POST_CREATE_SPM + id, payload);
  }

  getListDistributorType() {
    return this.baseService.getData<IDistributorType[]>(API.LIST_TYPE).pipe(
      map((resp) => {
        if (!resp) {
          return [];
        }

        return resp.data.map((res) => {
          const inputSelector: InputSelectInterface = new InputSelectInterface();
          const { enum_value, enum_string } = res;
          inputSelector.value = enum_value;
          inputSelector.display = enum_string;
          return inputSelector;
        });
      })
    );
  }

  getListDistributorArea() {
    return this.baseService.getData<IDistributorArea[]>(API.LIST_DISTRIBUTOR_AREA).pipe(map((resp) => resp));
  }

  goToDetail = (id: string) => this.router.navigate([`/sales-marketing/program-marketing/${id}`]);
}
