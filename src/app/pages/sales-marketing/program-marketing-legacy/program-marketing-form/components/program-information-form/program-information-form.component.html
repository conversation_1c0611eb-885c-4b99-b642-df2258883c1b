<!--<p>program-information-form works!</p>-->
<h3 class="mb-10 fw-bold">Informasi Program</h3>
<!--<pre>form.value: {{FormValue|json}}</pre>-->
<!--<pre>form.valid: {{ form.valid }}</pre>-->
<ng-container *ngIf="(isLoadingSubject | async) === false; else loadingState">
  <app-note-view-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? ''" [title]="'Catatan Perbaikan'" />
  <div [formGroup]="form" class="form-group">
    <!-- nama program -->
    <div class="d-flex flex-wrap w-100 align-items-center mb-6">
      <label [class.required]="true" class="col-form-label col-12 col-lg-4">Nama Program</label>
      <div class="col-12 col-lg-8">
        <input [formControlName]="'program_name'" [required]="true" class="form-control form-control-solid" ngDefaultControl placeholder="Silahkan input nama program" />
      </div>
    </div>

    <!-- periode -->
    <div *ngIf="usePeriod" class="d-flex flex-wrap w-100 align-items-center my-6">
      <label [class.required]="true" class="col-form-label col-12 col-lg-4">Periode</label>
      <div class="col-12 col-lg-8">
        <div class="d-flex gap-3">
          <!--Start Period-->
          <div class="flex-column w-50">
            <p class="mb-1">Mulai</p>
            <app-input-date-picker [minDate]="calculateMinStartDate()" formControlName="period_start" ngDefaultControl />
          </div>

          <!--End Period-->
          <div class="flex-column w-50">
            <p class="mb-1">Berakhir</p>
            <app-input-date-picker [isDisabled]="!StartPeriod.value" [minDate]="calculateMinEndDate()" formControlName="period_end" ngDefaultControl />
          </div>
        </div>
      </div>
    </div>

    <!-- no.surat pengajuan -->
    <div class="d-flex flex-wrap w-100 align-items-center my-6">
      <label [class.required]="true" class="col-form-label col-12 col-lg-4">Nomor Surat Pengajuan</label>
      <div class="col-12 col-lg-8">
        <input
          [formControlName]="'reference_number'"
          [required]="true"
          class="form-control form-control-solid"
          ngDefaultControl
          placeholder="Silakan input berdasarkan nomor surat. Contoh: 01/03/2025/SULTANBATARA"
        />
      </div>
    </div>

    <!-- document -->
    <ng-container [formArrayName]="'document'">
      <ng-container *ngFor="let docField of Document.controls; let docIndex = index">
        <div [formGroupName]="docIndex" class="position-relative my-6">
          <app-document-upload
            (documentOutput)="handleDocumentOutput($event, docIndex)"
            [data]="handleDocumentData(docField.value)"
            [idx]="docIndex"
            [type]="getTypeDoc(docField.value.url)"
            [label]="'Dokumen'"
            [required]="true"
            [showLabel]="!(docIndex > 0)"
            formControlName="url"
            ngDefaultControl
          />
          <span
            (click)="removeDocumentField(docIndex)"
            *ngIf="docIndex > 0"
            [inlineSVG]="STRING_CONSTANTS.ICON.IC_TRASH_RED"
            class="cursor-pointer img-close-icon position-absolute mt-3 me-3 svg-icon svg-icon-4 top-0 end-0"
          ></span>
        </div>
      </ng-container>
      <div *ngIf="showAddDocument()" class="row">
        <div class="col-12 col-lg-8 offset-lg-4 text-center">
          <button (click)="addDocumentField()" class="btn bg-transparent border-0 p-0 btn-uploader" type="button">
            <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS_IMAGE_UPLOADER" class="svg-icon svg-icon-2"></span>
            <span class="text-info-label fw-bolder">Tambah Dokumen</span>
          </button>
        </div>
      </div>
    </ng-container>

    <!-- catatan management -->
    <div *ngIf="managementNote" class="d-flex flex-wrap w-100 align-items-center my-6">
      <label [class.required]="true" class="col-form-label col-12 col-lg-4">Catatan Managament</label>
      <div class="col-12 col-lg-8">
        <textarea
          [formControlName]="'management_note'"
          [required]="managementNote"
          class="form-control form-control-solid"
          ngDefaultControl
          placeholder="Silakan input catatan"
          rows="1"
        ></textarea>
      </div>
    </div>
    <button
      *ngIf="!managementNote"
      (click)="handleChangeNote(true)"
      type="button"
      mat-button
      class="d-flex align-items-center btn btn-outline btn-outline-secondary text-primary w-fit me-lg-3 px-5 my-6"
    >
      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS" class="svg-icon my-auto svg-icon-24"></span>
      Tambah Catatan Management
    </button>
    <a *ngIf="managementNote" (click)="handleChangeNote(false)" class="text-danger d-flex align-items-center fs-14 fw-bold cursor-pointer my-6"
      ><span [inlineSVG]="STRING_CONSTANTS.ICON.IC_TRASH_RED" class="svg-icon svg-icon-24 me-3"></span>Hapus Catatan Management</a
    >
  </div>
</ng-container>

<ng-template #loadingState>
  <div class="d-flex flex-column justify-content-center align-items-center my-5">
    <mat-spinner></mat-spinner>
    <div class="my-4">Loading Form</div>
  </div>
</ng-template>
