import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IChips, IChipsWithArea } from '@shared/components/v1/chips/chips.interface';
import { FormControl, FormControlName, FormGroup, FormGroupDirective } from '@angular/forms';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { BehaviorSubject, debounceTime, Observable } from 'rxjs';
import { IEndpointData } from '../../../../../products/Products.interface';
import { BaseResponse } from '@shared/base/base-response';
import { BaseService } from '@services/base-service.service';
``;
@Component({
  selector: 'app-input-list-box-distributor-legacy',
  templateUrl: './input-list-box-distributor-legacy.component.html',
  styleUrls: ['./input-list-box-distributor-legacy.component.scss'],
})
export class InputListBoxDistributorLegacyComponent implements OnInit, AfterViewInit, OnChanges {
  @ViewChild('modalForm') private modalFormComponent: ModalComponent;

  @Input() required: boolean = false;
  @Input() label: string;
  @Input() chipsListData: IChipsWithArea[] = [];
  @Input() ctaLabelAddItems: string;
  @Input() ctaLabelOpenModalForm: string;
  @Input() searchLabel: string;
  @Input() modalConfirmTitle: string;
  @Input() isPlantTypeList: boolean = true;
  @Input() UrlEndpoint: string;
  @Input() ListValue: BehaviorSubject<IChipsWithArea[] | any> = new BehaviorSubject<IChipsWithArea[] | any>([]);
  @Input() haveDescription: boolean = false;
  @Input() descriptionKey: string = '';
  @Input() showChipsModal: boolean = true;
  @Input() disabled: boolean = false;

  @Input() btnStyleOutline: boolean = false;
  @Input() btnStyleDisabled: boolean = false;

  @Input() initListBoxWitParam: boolean = false;
  @Input() endPointWithParam: IEndpointData;
  @Input() outputAll = false; // send only id on setFormControlValue;

  @Input() useIndeterminateCheckbox = false; // use check all
  @Input() labelCheckAll = '';
  @ViewChild('checkboxAll') checkboxAll: ElementRef;

  @Input() customColClass = { label: 'col-lg-4', content: 'col-lg-8' };
  @Output() selectionOutput = new EventEmitter();

  modalFormConfig: ModalConfig;
  searchControl: FormControl;
  listbox_listItems: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  listbox_selectedItems: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  STRING_CONSTANTS = STRING_CONSTANTS;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  listbox_listItems$: Observable<BaseResponse<any[]>>;

  valueFormGroup: FormGroup;
  valueFormControl?: FormControl;

  constructor(private baseService: BaseService, private formGroupDirective: FormGroupDirective, private formControlNameDirective: FormControlName, private cdr: ChangeDetectorRef) {
    this.searchControl = new FormControl('');
  }

  ngOnInit(): void {
    this.initModalForm();
    this.initListBox();
    this.changeKeyword();

    this.valueFormGroup = this.formGroupDirective.form;
    this.valueFormControl = this.formGroupDirective.getControl(this.formControlNameDirective);

    if (this.ListValue.value.length) {
      this.handleValueChanges();
    }
  }

  handleValueChanges() {
    this.valueFormControl?.valueChanges.subscribe((value) => {
      if (value.length > 0) {
        this.ListValue.next(value);
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.listBoxItems && !changes.listBoxItems.firstChange) {
      this.listbox_listItems = changes.listBoxItems.currentValue;
    }

    if (changes.chipsListData && !changes.chipsListData.firstChange && !!changes.chipsListData.currentValue?.length) {
      this.chipsListData = changes.chipsListData.currentValue;
      this.setSelectedChipsList();
      this.mapSelectedItems(this.listbox_listItems.value);
    }

    if (changes.chipsListData && changes.chipsListData.firstChange && !!changes.chipsListData.currentValue?.length) {
      this.listbox_selectedItems.next(changes.chipsListData.currentValue);
    }

    if (changes.endPointWithParam && !changes.endPointWithParam.firstChange) {
      this.endPointWithParam = changes.endPointWithParam.currentValue;
      this.getListBoxDataWithParam(this.endPointWithParam);
    }

    if (changes.modalConfirmTitle && !changes.modalConfirmTitle.firstChange) {
      this.modalConfirmTitle = changes.modalConfirmTitle.currentValue;
      this.initModalForm();
    }

    if (changes.UrlEndpoint && !changes.UrlEndpoint.firstChange) {
      this.listbox_listItems.next([]);
      this.chipsListData = [];
      this.initListBox();
    }
  }

  initModalForm() {
    this.modalFormConfig = {
      modalTitle: this.modalConfirmTitle,
      onClose: () => {
        this.toggleShowSelectedList();
        return true;
      },
    };
  }

  initListBox() {
    this.btnStyleDisabled = this.disabled;
    this.isLoading.next(true);
    return this.initListBoxWitParam ? this.getListBoxDataWithParam(this.endPointWithParam) : this.getListBoxData();
  }

  getListBoxData(keyword?: string) {
    const _keyword = keyword ? '?keyword=' + keyword : '';
    this.listbox_listItems$ = this.baseService.getData(this.UrlEndpoint + _keyword);
    this.getData();
  }

  getListBoxDataWithParam(data: IEndpointData) {
    if (!Object.keys(data).length) {
      return;
    }

    const { api, body } = data;
    this.listbox_listItems$ = this.baseService.postData(api, body);

    this.getData();
  }

  getData() {
    this.listbox_listItems$.subscribe((list) => {
      if (list) {
        this.setSelectedChipsList();

        let { data } = list;

        if (!data) {
          return;
        }

        const _excludedEmptyName = data.filter((item) => item.name != null && item.name !== '');
        _excludedEmptyName.map((el) => (el.selected = false));

        data = _excludedEmptyName;
        const ids = this.listbox_listItems.value.map((item) => item.id);
        data = data.filter((item) => {
          return !ids.includes(item.id);
        });

        this.listbox_listItems.next([...this.listbox_listItems.value, ...data]);
        this.mapSelectedItems(this.listbox_listItems.value);
        this.cdr.detectChanges();
      }
    });
  }

  mapSelectedItems(listItems: any[]) {
    if (!this.chipsListData.length) {
      return;
    }

    let _list: any[] = [];
    listItems.map((listBoxItem) => {
      const merged = [...this.listbox_selectedItems.value, ...this.chipsListData].filter((chips) => chips.selected);
      const seen = new Set();

      merged.forEach((chips) => {
        if (chips.id === listBoxItem.id && !seen.has(chips.id)) {
          listBoxItem.selected = true;
          _list.push(listBoxItem);
          seen.add(chips.id);
        }
      });
    });

    this.listbox_selectedItems.next(_list);
    if (this.chipsListData && this.chipsListData.length > 0) this.selectionOutput.emit(this.listbox_selectedItems);
  }

  handleChangeSelection() {
    this.setSelectedItems();
  }

  setSelectedItems() {
    this.listbox_selectedItems.next(this.listbox_listItems.value.filter((val: any) => val.selected));
  }

  handleDataOutput(items: IChips[]) {
    this.listbox_selectedItems.next(items);
    this.selectionOutput.emit(this.listbox_selectedItems);

    this.setFormControlValue();
    this.handleRemovedChips();
    if (this.useIndeterminateCheckbox) return this.getCheckedAll();
  }

  handleAddChipsList() {
    this.selectionOutput.emit(this.listbox_selectedItems);
    this.toggleShowSelectedList();

    this.setFormControlValue();
    return this.modalFormComponent.dismiss();
  }

  setFormControlValue() {
    const data = this.listbox_selectedItems.value;
    let _arrId: string[] = [];
    data.forEach((item) => _arrId.push(item.id));
    this.valueFormControl?.setValue(this.outputAll ? this.listbox_selectedItems.value : _arrId);
  }

  toggleShowSelectedList() {
    this.chipsListData = this.listbox_selectedItems.value;
  }

  handleOpenModalForm = () => {
    this.modalFormComponent.open().then();
    if (this.chipsListData.length) this.getCheckedAll();
  };

  setSelectedChipsList() {
    this.listbox_selectedItems.value.map((chips) => (chips.selected = true));
    this.isLoading.next(false);
  }

  handleRemovedChips() {
    const _findRemovedChips = this.chipsListData.filter((chips) => !chips.selected);
    const _listBoxItems = this.listbox_listItems.value;

    _listBoxItems.map((listBoxItem) => {
      _findRemovedChips.map((item) => {
        if (listBoxItem.id === item.id) {
          listBoxItem.selected = item.selected;
        }
      });
    });

    this.listbox_listItems.next(_listBoxItems);
  }

  getCheckedAll() {
    if (!this.useIndeterminateCheckbox) return;
    const selectedItemsLength = this.listbox_selectedItems.value.length;
    const listItemsLength = this.listbox_listItems.value.length;

    const isCheckedAll = selectedItemsLength === listItemsLength;

    let checkedValue = false;
    let indeterminateValue = false;

    if (!selectedItemsLength) {
      checkedValue = false;
      indeterminateValue = false;
    }

    if (selectedItemsLength > 0 && selectedItemsLength < listItemsLength) {
      checkedValue = false;
      indeterminateValue = true;
    }

    if (isCheckedAll) {
      checkedValue = true;
      indeterminateValue = false;
    }

    this.checkboxAll.nativeElement.checked = checkedValue;
    this.checkboxAll.nativeElement.indeterminate = indeterminateValue;
  }

  handleCheckAll(e: any) {
    const selectedValue = e.target.checked;
    this.listbox_listItems.value.map((item) => (item.selected = selectedValue));
  }

  changeKeyword() {
    this.searchControl.valueChanges.pipe(debounceTime(300)).subscribe((value: string) => {
      if (value.length < 3) {
        return;
      }

      this.isLoading.next(true);
      this.getListBoxData(value);
      return;
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.valueFormControl?.value && this.showChipsModal) {
        if (!this.chipsListData.length) this.chipsListData = this.valueFormControl.value;
      }
    });
  }
}
