<h3 class="mb-10 fw-bold">Informasi Kompensasi</h3>
<!--<pre>{{ form.value | json }}</pre>-->
<ng-container *ngIf="(isLoadingSubject | async) === false; else loadingState">
  <app-note-view-revision *ngIf="!!data?.revision_note" [note]="data?.revision_note ?? ''" [title]="'Catatan Perbaikan'" />
  <div [formGroup]="form" class="form-group">
    <div class="d-flex flex-wrap my-4">
      <label [class.required]="true" class="col-form-label col-12 col-lg-4">Distributor</label>
      <div class="col-12 col-lg-8">
        <app-input-select-autocomplete
          #inputSelectDistributor
          (selectedValue)="onSelectedDistributor($event)"
          [fetchDataFn]="getDistributor"
          [formControlName]="'distributor'"
          [searchParamKey]="'keyword'"
          ngDefaultControl
          placeholder="Pilih salah satu"
        />
      </div>
    </div>

    <div class="d-flex flex-wrap my-4">
      <label [class.required]="true" class="col-form-label col-12 col-lg-4">Alamat Pengiriman</label>
      <div class="col-12 col-lg-8">
        <app-input-select-material
          #inputSelectAddress
          (handleChangeData)="handleAddressChange($event)"
          [class]="'col-lg-12'"
          [formControlName]="'delivery_address_id'"
          [optionsData]="listAddressSubject"
          [readOnly]="!DistributorIdForm.value"
          [required]="true"
          [useFilterList]="true"
          ngDefaultControl
          placeholder="Pilih salah satu"
        ></app-input-select-material>
      </div>
    </div>

    <div class="d-flex flex-wrap my-4">
      <div class="col-12 col-lg-12">
        <fieldset>
          <app-input-list-box-group
            (selectionOutput)="productSelection($event)"
            [UrlEndpoint]="API.PROGRAM_MARKETING.GET_PRODUCT_GROUP"
            [chipsListData]="valueProduct"
            [disabled]="false"
            [formControlName]="'product'"
            [required]="true"
            [showChipsModal]="false"
            ctaLabelAddItems="Tambahkan"
            ctaLabelOpenModalForm="Tambah Produk"
            label="Produk Dibeli"
            modalConfirmTitle="Tambah Produk"
            ngDefaultControl
            searchLabel="Pilih Produk"
            title="Pilih Produk"
          >
          </app-input-list-box-group>
        </fieldset>
      </div>
    </div>

    <div *ngFor="let variant of VariantForm.controls; index as i" [formArrayName]="'variants'" class="my-4">
      <div [formGroupName]="i" class="d-flex flex-wrap my-4">
        <div class="col-form-label col-12 col-lg-4">
          <label [class.required]="true" class="w-label"> Jumlah Kompensasi {{ variant.get('variant_name')?.value || variant.get('name')?.value }} </label>
        </div>
        <div class="col-12 col-lg-8 d-flex">
          <div class="col-6">
            <div class="font-12 mb-4">QTY</div>
            <div class="input-group input-group-solid me-4">
              <input
                (input)="changeQtyVariant($event, i, variant.get('delivery_ratio')?.value)"
                [formControlName]="'qty'"
                [required]="true"
                appNumberInput
                class="form-control form-control-solid"
                ngDefaultControl
                placeholder="Input minimal berat/volume"
              />
              <span class="input-group-text px-6 text-capitalize ms-n5 font-12 text-gray-700">{{ variant.get('sale_unit')?.value | lowercase }}</span>
            </div>
          </div>
          <div class="col-6 ms-4">
            <div class="font-12 mb-4">Volume/Berat</div>
            <div class="input-group input-group-solid">
              <input
                [formControlName]="'weight'"
                [required]="true"
                class="form-control form-control-solid bg-gray-700"
                ngDefaultControl
                placeholder="Input minimal berat/volume"
                readonly
              />
              <span class="input-group-text px-6 text-capitalize ms-n5 font-12 text-gray-700">L</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loadingState>
  <div class="d-flex flex-column justify-content-center align-items-center my-5">
    <mat-spinner></mat-spinner>
    <div class="my-4">Loading Form</div>
  </div>
</ng-template>
