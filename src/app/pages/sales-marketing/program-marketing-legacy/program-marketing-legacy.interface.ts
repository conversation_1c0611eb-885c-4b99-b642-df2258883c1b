import {
  EnumInclusionProgramTermType,
  EnumLegacyProgramMarketingDiscount,
  EnumProgramMarketingDiscountCategory,
  EnumProgramMarketingDiscountProgramType,
  EnumProgramMarketingDiscountScope,
  EnumProgramMarketingDiscountType,
  EnumProgramMarketingPOType,
  EnumProgramMarketingQuotaType,
  EnumProgramMarketingRewardType,
  EnumProgramMarketingScope,
  EnumProgramMarketingStatus,
  EnumProgramMarketingType,
  EnumVerificationStatus,
} from './program-marketing.enum';
import { IGenericActorModifier, IGenericCheckbox, IGenericEpochPeriod, IGenericIdName, IGenericNameUrl, IGenericValueDisplay } from '@shared/interface/generic';
import { IListboxRegion } from '@shared/components/form/input-listbox-region/input-listbox-region.interface';

export interface CounterList {
  submitted: number;
  need_changed: number;
  active: number;
  scheduled: number;
  finished: number;
  [key: string]: number; // for any additional statuses
}

export interface IProgramMarketing extends ProgramMarketingType {
  program_name: string;
  scope: string[];
  period_start: string; // yyyy-mm-dd
  period_end: string; // yyyy-mm-dd
  status_enum: string;
  status_string: string;
  created_date: number; // epoch
}

// todo: replace legacy program marketing detail interface
// export interface ILegacyProgramMarketingDetail {
//   header: IProgramMarketingDetail__Header;
//   information: IProgramMarketingDetail__ProgramInformation;
//   program_term: IProgramMarketingDetail__ProgramTerm;
//   order_term: IProgramMarketingDetail__OrderTerm;
//   document_url: string;
// }

// export interface ILegacyProgramMarketingDetail__Header extends ProgramMarketingStatus {
//   name: string;
//   created: IGenericActorModifier;
//   finance_approved: IGenericActorModifier;
//   modified: IGenericActorModifier;
// }

// export interface ILegacyProgramMarketingDetail__Information extends ProgramMarketingType, IGenericEpochPeriod {
//   status_verified_enum?: EnumVerificationStatus | null;
//   management_note: string;
//   revision_note: string;
// }

interface ProgramMarketingType {
  program_type_enum: EnumProgramMarketingType;
  program_type_string?: string;
}

interface ProgramMarketingScope {
  scope_string: string;
  scope_enum: EnumProgramMarketingScope; // NATIONAL, AREA, DISTRIBUTOR
}

interface ProgramMarketingStatus {
  status_program_marketing_enum: EnumProgramMarketingStatus;
  status_program_marketing_string: string;
}

export interface IProgramMarketingDetailLegacy {
  header: IProgramMarketingDetail__Header;
  information: IProgramMarketingDetail__ProgramInformation;
  program_term: IProgramMarketingDetail__ProgramTerm;
  order_term: ILegacyProgramMarketingDetail__OrderTerm;
  discount_term: IProgramMarketingDetail__DiscountTerm;
  reward: IProgramMarketingDetail__RewardTerm;
  compensation: ICompensationTerm;
  document_url: IGenericNameUrl[];
}

export interface IProgramMarketingDetail__Header extends ProgramMarketingStatus {
  name: string;
  created: IGenericActorModifier;
  finance_approved: IGenericActorModifier;
  modified: IGenericActorModifier;
}

// PROGRAM INFORMATION
export interface IProgramMarketingDetail__ProgramInformation extends ProgramMarketingType, IGenericEpochPeriod {
  status_verified_enum: EnumVerificationStatus | null;
  reference_number: string;
  management_note: string;
  revision_note: string;
  extend_period_end: number;
}

// PROGRAM TERM
export interface IProgramMarketingDetail__ProgramTerm extends ProgramMarketingScope {
  status_verified_enum: EnumVerificationStatus | null;
  selected_area_ids: string[];
  selected_distributor_ids: string[];
  program_marketing_discount_enum: EnumProgramMarketingDiscountProgramType;
  maximal_discount: number;
  quota: { enum_type: EnumProgramMarketingQuotaType; max_qty: number; scope_list: string[] };
  long_term_distributor_inclusion: EnumInclusionProgramTermType;
  excluded_distributor_program: IGenericIdName[];
  retailer_inclusion: EnumInclusionProgramTermType;
  excluded_retailer_program: IGenericIdName[];
  revision_note: string;
}

// ORDER TERM
export interface ILegacyProgramMarketingDetail__OrderTerm {
  status_verified_enum: EnumVerificationStatus | null;
  purchase_order_type_enum: EnumProgramMarketingPOType;
  bundling: OrderTerm__Bundling;
  accumulation: OrderTerm__Accumulation;
  reward: OrderTerm__Reward;
  revision_note: string;
}

export interface OrderTerm__Bundling {
  variants: OrderTerm__Variant[];
}

export interface OrderTerm__Accumulation {
  variants: {
    id: string;
    name: string;
    sale_unit: string;
    delivery_unit: string;
    delivery_ratio: number;
  }[];
  minimum_weight_or_volume: number;
  minimum_qty: number;
  minimum_price: number;
}

interface OrderTerm__Variant {
  id: string;
  name: string;
  qty: number;
  sale_unit: string;
  delivery_unit: string;
  delivery_ratio: number;
}

export interface OrderTerm__Reward {
  reward_type_enum: EnumProgramMarketingRewardType; // MAI_PRODUCT, NON_MAI_PRODUCT
  non_mai_product: {
    product_other_reward: string;
    maximum_budget_other_reward: number;
  };
  mai_product: {
    variant_id: string;
    name: string;
    qty: number;
    unit: string;
  };
}

// DISCOUNT TERM
export interface IProgramMarketingDetail__DiscountTerm {
  status_verified_enum: EnumVerificationStatus | null;
  discount_scope: EnumProgramMarketingScope;
  discount_category: EnumProgramMarketingDiscountCategory;
  discount_type_enum: EnumProgramMarketingDiscountType;
  discount_products: DiscountTerm__DiscountProduct[];
  discount_purchases: string[];
  revision_note: string;
}

export interface DiscountTerm__DiscountProduct {
  variant_name: string;
  discount: string;
  minimum_order: string;
}

// REWARD TERM
export interface IProgramMarketingDetail__RewardTerm extends Omit<IRewardTerm, 'multiplication'> {
  status_verified_enum: EnumVerificationStatus | null;
  multiplication_type: string;
}

// COMPENSATION
// export interface IProgramMarketingDetail__Compensation extends ICompensationTerm {}
export interface IRevisionNote {
  information: string | null;
  program_term: string | null;
  order_term: string | null;
  discount_term: string | null;
  reward: string | null;
  compensation: string | null;
  order_and_discount_term: string | null;
}

export interface IPayloadProgramVerification {
  revision_note: IRevisionNote;
}

export interface IProductRewardProgramMarketing {
  variant_id: string;
  variant_name: string;
}

export interface ISalesDiscountProgramMarketing {
  area_name: string;
  discount_percentage: number;
  discount_level: number;
}

export interface IParamSalesDiscount {
  type: string;
  value: string[];
}

export interface IRadioBtnFormProgramMarketing extends IGenericValueDisplay {
  description?: string;
  disable?: boolean;
}

export interface IListboxArea extends IListboxRegion {}

// Product Group brand - variant
export interface IProductGroupBrand extends IGenericCheckbox {
  brand_name: string;
  brand_id: string;
  variants: IProductGroupBrand__Variant[];
}

export interface IProductGroupBrand__Variant extends IGenericCheckbox {
  id: string;
  variant_name: string;
  delivery_unit: string;
  sale_unit: string;
  delivery_ratio: number;
  price_before_tax: number;
}

// Payload new
export interface IInformation {
  program_name: string;
  period_start: string;
  period_end: string;
  management_note: string;
  document_url: string;
  reference_number?: string;
  revision_note?: string;
} // its legacy information

export interface IProgramInformation {
  program_name: string;
  period_start: string; // yyyy-mm-dd
  period_end: string; // yyyy-mm-dd
  management_note: string;
  reference_number: string;
  document_url: IGenericNameUrl[];
  revision_note?: string;
}

export interface IDistributorOption {
  id: string;
  name: string;
  area?: string;
  code?: string;
}

export interface IProgramTerm {
  scope_enum: EnumProgramMarketingScope;
  area_ids: never[];
  sub_area_ids: never[];
  distributor_ids: IDistributorOption[];
  revision_note: string;
  quota: {
    enum_type: string;
    max_qty: any;
    scope_list: never[];
  };
}

export interface IDiscountProduct {
  variant_id: string;
  variant_name: string;
  discount_type_enum: EnumLegacyProgramMarketingDiscount;
  maximum_discount: number;
  delivery_ratio: number;
  minimum_order: number;
  sale_unit: string;
  price_before_tax: number;
}

export interface IDiscountTerm {
  discount_scope: EnumProgramMarketingDiscountScope;
  discount_type_enum: EnumProgramMarketingDiscountProgramType;
  discount_category_enum: EnumProgramMarketingDiscountCategory;
  maximum_discount: number;
  products: IDiscountProduct[];
  revision_note: string;
}

export interface IProductVariant {
  id: string;
  name: string;
}

export interface IBundlingVariant {
  variant_id: string;
  name: string;
  delivery_ratio: number;
  qty: number;
}

export interface IBundling {
  variants: IBundlingVariant[];
}

export interface IAccumulation {
  variant_ids: any[];
  minimum_weight_or_volume: number;
  minimum_qty: number;
  minimum_price: number;
}

export interface IOrderTerm {
  purchase_order_type: EnumProgramMarketingPOType;
  product_variant: IProductVariant[];
  bundling?: IBundling; // Required if purchase_order_type == "BUNDLING"
  accumulation?: IAccumulation; // Required if purchase_order_type == "ACCUMULATION"
  revision_note: string;
}

interface NonMaiProduct {
  product_other_reward: string;
  maximum_budget_other_reward: number;
}

interface MaiProduct {
  variant: {
    id: string;
    name: string;
  };
  qty: number;
  name?: string;
  unit?: string;
}

export interface IRewardTerm {
  multiplication: boolean; // true == MULTIPLE, false == SINGLE
  reward_type_enum: EnumProgramMarketingRewardType;
  non_mai_product?: NonMaiProduct; // Required if reward_type_enum == "NON_MAI_PRODUCT"
  mai_product?: MaiProduct; // Required if reward_type_enum == "MAI_PRODUCT"
  revision_note: string;
}

export interface IProgramMarketing {
  information: IProgramInformation;
  program_term: IProgramTerm;
  discount_term: IDiscountTerm;
  order_term: IOrderTerm;
  reward: IRewardTerm;
  compensation: ICompensationTerm;
}

// interface INonProductReward {
//   product_other_reward: string;
//   maximum_budget_other_reward: number;
// }

// export interface IVariantQty {
//   variant_id: string | null;
//   name?: string | null;
//   delivery_ratio?: number | null;
//   qty: number | null;
// }

// interface IReward {
//   reward_type_enum: EnumProgramMarketingRewardType.MAI_PRODUCT | EnumProgramMarketingRewardType.NON_MAI_PRODUCT;
//   non_mai_product?: INonProductReward; // for NON_MAI_PRODUCT
//   mai_product?: IVariantQty; // for MAI_PRODUCT
// }

// export interface IOrderTerm {
//   purchase_order_type: EnumProgramMarketingPOType.BUNDLING | EnumProgramMarketingPOType.ACCUMULATION;
//   product_variant?: IGenericIdName[];
//   bundling?: IBundling; // for BUNDLING
//   accumulation?: IAccumulation; // for ACCUMULATION
//   reward: IReward;
//   revision_note?: string;
// }

export interface IPayloadProgramMarketing {
  information: IProgramInformation;
  program_term: IProgramTerm;
  order_term: IOrderTerm;
  discount_term: IDiscountTerm;
  reward: IRewardTerm;
  compensation: ICompensationTerm;
}

// type StatusVerifiedEnum = 'VERIFIED' | 'REQUEST_REVISION'; // use EnumVerificationStatus

interface IProduct {
  variant_id: string;
  variant_name: string;
  qty: string;
  qty_process: string;
  sale_unit: string;
  delivery_ratio: number;
}

export interface ICompensationTerm {
  status_verified_enum: EnumVerificationStatus | null;
  distributor_name: string;
  distributor_id?: string;
  delivery_address: string;
  delivery_address_id?: string;
  products: IProduct[];
  is_fulfilled?: boolean; // for only create spm compensation
  revision_note: string;
}

// promag form payload
export interface IPromagFormPayload {
  discount_term: IPromagFormPayload__DiscountTerm;
}

// discount term payload
export interface IPromagFormPayload__DiscountTerm {
  // oneshoot
  discount_scope?: EnumProgramMarketingDiscountScope;
  discount_type_enum?: EnumProgramMarketingDiscountProgramType;
  maximum_discount?: number;
  // discount purchase
  discount_category_enum?: EnumProgramMarketingDiscountCategory;
  products: IPromagFormPayload__DiscountTermProduct[];
}

export interface IPromagFormPayload__DiscountTermProduct {
  variant_id: string;
  discount_type_enum?: EnumProgramMarketingDiscountProgramType;
  maximum_discount: number;
  minimum_order?: number;
}

export interface IDetailListPO {
  total_purchase_order: number;
  quota: IQuotaListPO;
}

export interface IQuotaListPO {
  remaining: number;
  total: number;
  details: IScope[];
}

interface IScope {
  scope_name: string;
  remaining: number;
  total: number;
}

export interface IResponseProgramMarketing {
  program_marketing_id: string;
  message?: string;
}

export interface ISelectedScope extends IGenericIdName {
  area?: string;
}

export interface IListSpmCompensation {
  id: string;
  code: string;
  product_list: string[];
  qty: number;
  qty_process: number;
  created_date: string; // ISO date format
  status_enum: string; // Add other possible statuses if needed
  status_enum_string: string;
}

export interface IModalConfirmationProgramMarketing extends IProgramMarketing {}

export interface IPayloadExtendPeriod {
  period_end: string;
  document: IGenericNameUrl;
}

// todo: remove unused
