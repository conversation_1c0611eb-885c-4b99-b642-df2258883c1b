export enum EnumProgramMarketingScope {
  NATIONAL = 'NATIONAL',
  AREA = 'AREA',
  SUB_AREA = 'SUB_AREA',
  DISTRIBUTOR = 'DISTRIBUTOR',
  RETAILER = 'RETAILER',
}

export enum EnumProgramMarketingType {
  ONE_SHOOT = 'ONE_SHOOT',
  DISCOUNT_PRODUCT = 'DISCOUNT_PRODUCT',
  DISCOUNT_PURCHASE = 'DISCOUNT_PURCHASE',
  PRODUCT_COMPENSATION = 'PRODUCT_COMPENSATION',
}
export enum EnumProgramMarketingTypeString {
  ONE_SHOT = 'One Shoot', // temp.fix inconsistent response key name
  DISCOUNT_PRODUCT = 'Diskon Produk',
  DISCOUNT_PURCHASE = 'Diskon dengan Pembelian',
  PRODUCT_COMPENSATION = 'Kompensasi Produk',
}

export enum EnumProgramMarketingStatus {
  SUBMITTED = 'SUBMITTED',
  NEED_CHANGED = 'NEED_CHANGED',
  ACTIVE = 'ACTIVE',
  SCHEDULED = 'SCHEDULED',
  FINISHED = 'FINISHED',
}

export enum EnumProgramMarketingStatusString {
  SUBMITTED = 'Diajukan',
  NEED_CHANGED = 'Butuh Perbaikan',
  ACTIVE = 'Aktif',
  SCHEDULED = 'Dijadwalkan',
  FINISHED = 'Selesai',
}

export enum EnumProgramMarketingSection {
  INFORMATION = 'INFORMATION',
  ORDER_TERM = 'ORDER_TERM',
  PROGRAM_TERM = 'PROGRAM_TERM',
  REWARD = 'REWARD', // ketentuan hadiah
  DISCOUNT_TERM = 'DISCOUNT_TERM', // ketentuan diskon
  COMPENSATION = 'COMPENSATION',
  ORDER_AND_DISCOUNT_TERM = 'ORDER_AND_DISCOUNT_TERM', // pengaturan pembelian dan diskon
}

export enum EnumVerificationStatus {
  VERIFIED = 'VERIFIED',
  REQUEST_REVISION = 'REQUEST_REVISION',
}

export enum EnumLegacyProgramMarketingDiscount {
  EQUAL_SALES_DISCOUNT = 'EQUAL_SALES_DISCOUNT',
  SET_MAXIMUM_SALES_DISCOUNT = 'SET_MAXIMUM_SALES_DISCOUNT',
  NO_SALES_DISCOUNT = 'NO_SALES_DISCOUNT',
  FIXED_SALES_DISCOUNT = 'FIXED_SALES_DISCOUNT',
}
export enum EnumLegacyProgramMarketingDiscountString {
  EQUAL_SALES_DISCOUNT = 'Normal',
  SET_MAXIMUM_SALES_DISCOUNT = 'Maksimal',
  NO_SALES_DISCOUNT = 'Tanpa Sales Discount',
}

export enum EnumProgramMarketingPOType {
  BUNDLING = 'BUNDLING',
  ACCUMULATION = 'ACCUMULATION',
}

export enum EnumProgramMarketingPOTypeString {
  BUNDLING = 'Bundling',
  ACCUMULATION = 'Akumulasi Produk',
}

export enum EnumProgramMarketingRewardType {
  MAI_PRODUCT = 'PRODUCT_MAI',
  NON_MAI_PRODUCT = 'PRODUCT_NON_MAI',
}

export enum EnumProgramMarketingAccumulationType {
  MINIMUM_WEIGHT_OR_VOLUME = 'minimum_weight_or_volume',
  MINIMUM_QTY = 'minimum_qty',
  MINIMUM_PRICE = 'minimum_price',
}

export enum EnumProgramMarketingQuotaType {
  ALL = 'ALL',
  PER_SCOPE = 'PER_SCOPE',
  UNLIMITED = 'UNLIMITED',
}

export enum EnumProgramMarketingQuotaTypeString {
  ALL = 'Kuota Keseluruhan',
  PER_SCOPE = 'Kuota Per Cakupan',
  UNLIMITED = 'Tanpa Kuota',
}

export enum EnumProgramMarketingDiscountProgramType {
  EQUAL_SALES_DISCOUNT = 'EQUAL_SALES_DISCOUNT',
  SET_MAXIMUM_SALES_DISCOUNT = 'SET_MAXIMUM_SALES_DISCOUNT',
  FIXED_SALES_DISCOUNT = 'FIXED_SALES_DISCOUNT',
  NO_SALES_DISCOUNT = 'NO_SALES_DISCOUNT',
  ONE_SHOOT_DISCOUNT = 'ONE_SHOOT_DISCOUNT',
  AVAILABLE_TO_USE_DISCOUNT_PRODUCT = 'AVAILABLE_TO_USE_DISCOUNT_PRODUCT',
}
export enum EnumProgramMarketingDiscountProgramTypeString {
  ONE_SHOOT_DISCOUNT = 'Memiliki Diskon khusus Program',
  AVAILABLE_TO_USE_DISCOUNT_PRODUCT = 'Mengikuti Program Diskon yang berlaku',
  EQUAL_SALES_DISCOUNT = 'Maksimal Sales Diskon Level 2',
  SET_MAXIMUM_SALES_DISCOUNT = 'Atur Maksimal Diskon',
  FIXED_SALES_DISCOUNT = 'Atur Diskon Fixed',
  NO_SALES_DISCOUNT = 'Tanpa Diskon',
}

export enum EnumProgramMarketingDiscountScope {
  ALL = 'ALL',
  PER_PRODUCT = 'PER_PRODUCT',
}
export enum EnumProgramMarketingDiscountScopeString {
  ALL = 'Keseluruhan Program',
  PER_PRODUCT = 'Per Produk',
}

export enum EnumProgramMarketingDiscountCategory {
  PERCENTAGE = 'PERCENTAGE',
  NOMINAL = 'NOMINAL',
}
export enum EnumProgramMarketingDiscountCategoryString {
  PERCENTAGE = 'Presentase (%)',
  NOMINAL = 'Nominal (Rp)',
}

export enum EnumProgramMarketingDiscountType {
  SET_MAXIMUM_SALES_DISCOUNT = 'SET_MAXIMUM_SALES_DISCOUNT',
  FIXED_SALES_DISCOUNT = 'FIXED_SALES_DISCOUNT',
}
export enum EnumProgramMarketingDiscountTypeString {
  SET_MAXIMUM_SALES_DISCOUNT = 'Jumlah Maksimal',
  FIXED_SALES_DISCOUNT = 'Tetap/Fixed',
}

export enum EnumProgramMarketingOrderUnitType {
  SALE_UNIT = 'SALE_UNIT',
  WEIGHT_UNIT = 'WEIGHT_UNIT',
}
export enum EnumProgramMarketingOrderUnitTypeString {
  SALE_UNIT = 'Satuan Jual (box/jerigen/bag)',
  WEIGHT_UNIT = 'Satuan Berat (L/kg)',
}

export enum EnumProgramMarketingDiscountProgramOneShootType {
  ONE_SHOOT_DISCOUNT = 'ONE_SHOOT_DISCOUNT',
  AVAILABLE_TO_USE_DISCOUNT_PRODUCT = 'AVAILABLE_TO_USE_DISCOUNT_PRODUCT',
  EQUAL_SALES_DISCOUNT = 'EQUAL_SALES_DISCOUNT',
  NO_SALES_DISCOUNT = 'NO_SALES_DISCOUNT',
}

export enum EnumProgramMarketingDiscountProgramOneShootTypeString {
  ONE_SHOOT_DISCOUNT = 'Memiliki Diskon khusus Program',
  AVAILABLE_TO_USE_DISCOUNT_PRODUCT = 'Mengikuti Program Diskon yang berlaku',
  EQUAL_SALES_DISCOUNT = 'Maksimal Sales Diskon Level 2',
  NO_SALES_DISCOUNT = 'Tanpa Diskon',
}

export enum EnumInclusionProgramTermType {
  INCLUDE_ALL = 'INCLUDE_ALL',
  EXCLUDE_ALL = 'EXCLUDE_ALL',
  EXCLUDE_PARTIAL = 'EXCLUDE_PARTIAL',
}

export enum EnumInclusionProgramTermTypeString {
  INCLUDE_ALL = 'Termasuk Hitungan',
  EXCLUDE_ALL = 'Dikecualikan dari Semua Program',
  EXCLUDE_PARTIAL = 'Sebagian Program Dikecualikan',
}

export enum EnumInclusionProgramTermDistributorString {
  INCLUDE_ALL = 'Produk dari program ini akan dimasukkan perhitungan seluruh Program Long Term yang Dijadwalkan dan Aktif',
  EXCLUDE_ALL = 'Produk dari program ini tidak akan dimasukkan ke dalam seluruh perhitungan Program Long Term',
  EXCLUDE_PARTIAL = 'Produk dari program ini tidak akan dimasukkan ke perhitungan Program Long Term yang dipilih',
}

export enum EnumInclusionProgramTermRetailerString {
  INCLUDE_ALL = 'Produk dari program ini akan dimasukkan perhitungan seluruh Program Retailer yang Dijadwalkan dan Aktif',
  EXCLUDE_ALL = 'Produk dari program ini tidak akan dimasukkan ke dalam seluruh perhitungan Program Retailer',
  EXCLUDE_PARTIAL = 'Produk dari program ini tidak akan dimasukkan ke perhitungan Program Retailer yang dipilih',
}
