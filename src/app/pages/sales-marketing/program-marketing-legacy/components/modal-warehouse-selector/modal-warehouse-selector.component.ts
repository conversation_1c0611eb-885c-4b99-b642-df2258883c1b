import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { IWarehouseItem, IWarehouseList } from '@shared/interface/warehouse.interface';
import { BehaviorSubject, Observable } from 'rxjs';
import { PurchaseOrderService } from '@pages/purchase-order/purchase-order.service';
import { BaseResponse } from '@shared/base/base-response';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-modal-warehouse-selector',
  templateUrl: './modal-warehouse-selector.component.html',
  styleUrls: ['./modal-warehouse-selector.component.scss'],
})
export class ModalWarehouseSelectorComponent implements OnInit, AfterViewInit {
  @ViewChild('modalWarehouseSelector') modalWarehouseSelector: ModalComponent;

  id: string;
  modalConfig = <ModalConfig>{
    modalTitle: '<PERSON><PERSON><PERSON>ng <PERSON>',
    showHeader: false,
    dismissButtonLabel: 'Lanjutkan',
    closeButtonLabel: 'Batal',
    onDismiss: () => this.onDismissModal(),
    disableDismissButton: () => !this.hasSelectedWarehouse(),
  };

  isLoading = new BehaviorSubject(false);
  listGudang$!: Observable<BaseResponse<IWarehouseList>>;
  selectedWarehouse: IWarehouseItem;

  constructor(private poService: PurchaseOrderService, private router: Router, private activeRoute: ActivatedRoute) {
    this.listGudang$ = this.poService.getListGudang();
    this.id = this.activeRoute.snapshot.params['id'];
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {}

  onDismissModal() {
    return this.router.navigate(['sales-marketing/program-marketing/create-spm/' + this.id], {
      queryParams: {
        warehouse_id: this.selectedWarehouse.id,
      },
    });
  }

  hasSelectedWarehouse() {
    return this.selectedWarehouse && !!Object.keys(this.selectedWarehouse).length;
  }
}
