<app-modal #modalProgramSelector [modalConfig]="modalConfig" [modalOptions]="{ size: 'md' }" [modalClass]="'modal-selector'">
  <div class="radio-card">
    <ng-container *ngFor="let item of programType; index as i; last as last">
      <div class="radio-card__item cursor-pointer p-6 mb-6" (click)="setSelectedProgram(item.enumType)" [class.active]="isSelectedProgram(item.enumType)">
        <div class="d-flex align-items-center">
          <span class="align-self-start svg-icon svg-icon-24" [inlineSVG]="item.icon"></span>
          <div class="radio-card__item-meta ps-4">
            <h4 class="title m-0">{{ item.title }}</h4>
            <p class="text-gray-700 my-2">{{ item.desc }}</p>
          </div>
          <div class="form-check m-0 ms-auto">
            <input
              type="radio"
              [value]="item.enumType"
              class="form-check-input w-20px h-20px cursor-pointer"
              id="flexRadioDefault{{ i }}"
              name="program"
              [(ngModel)]="selectedProgramType"
            />
          </div>
        </div>
      </div>
    </ng-container>
  </div>
  <div class="modal-footer d-flex flex-nowrap p-0 pt-8">
    <button type="button" color="primary" mat-raised-button class="btn btn-primary px-8" [disabled]="!hasSelectedProgram()" (click)="onDismiss()">
      {{ modalConfig.dismissButtonLabel }}
    </button>
  </div>
</app-modal>
