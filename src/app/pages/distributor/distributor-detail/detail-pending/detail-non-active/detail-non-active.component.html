<ng-container>
  <div class="my-7">
    <app-informasi-distributor></app-informasi-distributor>
  </div>
  <div *ngIf="ctaApprovalPending" class="d-flex justify-content-end align-items-center my-8">
    <button (click)="onRequest()" [disabled]="!impactedDistributor" class="btn btn-outline btn-outline-danger min-w-150px text-danger mx-4">Request Perbaikan</button>
    <button (click)="onVerification()" [disabled]="!impactedDistributor" mat-button color="primary" class="btn btn-primary btn-lg min-w-150px text-white">Verifikasi</button>
  </div>
</ng-container>

<app-modal #modalConfirmVerification [modalConfig]="modalConfigConfirm" [modalOptions]="{ size: 'md' }">
  <div class="text-center"><PERSON>gan menyetujui, status Distributor akan dinonaktifkan. Pastikan semua data telah lengkap dan benar.</div>
  <app-note-view
    *ngIf="impactedDistributor?.total_receivable"
    [classNoteView]="'mt-7'"
    [color]="'info'"
    [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
    [text]="'Distributor memiliki total piutang ' + utils.toRupiah(impactedDistributor?.total_receivable)"
  ></app-note-view>
</app-modal>

<app-modal #modalRequest [modalConfig]="modalConfigRequest" [modalOptions]="{ size: 'md' }">
  <div [formGroup]="form">
    <div class="mb-4">Silakan input catatan perbaikan penonaktifan distributor:</div>
    <textarea
      [formControlName]="'reject_reason'"
      [required]="true"
      class="form-control form-control-solid"
      ngDefaultControl
      placeholder="Silakan input catatan perbaikan penonaktifan Distributor."
      rows="4"
    ></textarea>
  </div>
</app-modal>

<app-modal #modalResponse [modalConfig]="modalResponseConfig" [modalOptions]="{ size: 'md' }">
  <ng-container>
    <div class="d-flex flex-column justify-content-center align-items-center">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
      <div class="my-5 text-center">{{ messageResponse ? messageResponse?.message : '' }}</div>
    </div>
    <app-note-view
      *ngIf="!RejectReasonForm.value"
      [classNoteView]="'mb-0'"
      [color]="'info'"
      [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
      [text]="'Distributor tidak lagi memiliki akses ke Website Distributor'"
    />
  </ng-container>
</app-modal>
