<ng-container *ngIf="dataResponse | async as data">
  <app-card-verification-form
    [title]="'Dokumen Verifikasi'"
    [isLoading]="isLoading"
    [bodyData]="bodyDataDocument.value"
    [cardNoteValue]="noteValue()"
    [status]="data.documents && data.documents.status_verified_enum"
    [typeSection]="EnumCardVerificationSection.DOCUMENT_VERIFY_KUL"
    [type]="EnumCardVerificationForm.DOCUMENT"
    (cardNoteChanged)="onNoteChanges($event)"
    (afterActionVerification)="afterActionVerification($event)"
  />
  <div class="d-flex justify-content-end align-items-center mt-8 mb-20">
    <button mat-button color="primary" class="btn btn-primary btn-lg text-white" (click)="modalConfirmVerification.open()" [disabled]="!isConfirmValid()">
      <span>Konfirmasi KUL</span>
    </button>
  </div>

  <app-modal #modalConfirmVerification [modalConfig]="modalConfigConfirmVerification" [modalOptions]="{ size: 'md' }">
    <!--    <pre>detailHeader: {{ detailHeader | json }}</pre>-->
    <div class="mb-n4">
      <p>Apakah anda yakin mengonfirmasi aktivasi Distributor:</p>
      <ng-container *ngIf="detailHeader as detail">
        <div class="row mb-4">
          <div class="col-12 col-md-4"><span class="text-gray-700">Nama Distributor</span></div>
          <div class="col-12 col-md-8">
            <span class="text-gray-700 me-2">:</span> <span>{{ detail.business_name ?? '-' }}</span>
          </div>
        </div>
        <div class="row mb-4 text-capitalize">
          <div class="col-12 col-md-4"><span class="text-gray-700">Cakupan</span></div>
          <div class="col-12 col-md-8">
            <span class="text-gray-700 me-2">:</span> <span>{{ detail.sales_area ?? '-' }}, {{ detail.sales_sub_area ?? '-' }}</span>
          </div>
        </div>
        <div class="row mb-4">
          <div class="col-12 col-md-4"><span class="text-gray-700">Regional Head</span></div>
          <div class="col-12 col-md-8">
            <span class="text-gray-700 me-2">:</span> <span>{{ detail.regional_head_name ?? '-' }}</span>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="hasRevisionNote(); else defaultTextTpl">
        <p class="mt-8">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_DANGER" class="svg-icon svg-icon-1 me-2"></span>
          <strong>Catatan Perbaikan</strong>
        </p>

        <div class="row mb-4">
          <div class="row mb-4" *ngFor="let rev of getRevisionNoteDataBody()">
            <div class="col-12 col-md-4">
              <span class="text-gray-700">{{ rev.label }}</span>
            </div>
            <div class="col-12 col-md-8">
              <span class="text-gray-700 me-2">:</span>
              <span>{{ rev.value }}</span>
            </div>
          </div>
        </div>
      </ng-container>

      <ng-template #defaultTextTpl>
        <p class="mb-0">Data distributor yang dikonfirmasi akan berubah menjadi <strong>Aktif</strong>. Pastikan semua data telah lengkap dan benar.</p>
      </ng-template>
    </div>
  </app-modal>

  <app-modal #modalResponseVerification [modalConfig]="modalConfigResponse" [modalOptions]="{ size: 'md' }">
    <div class="d-flex flex-column justify-content-center align-items-center mb-n6">
      <ng-container *ngIf="loadingSubmit | async; else responseTpl">
        <div class="mt-8">
          <mat-spinner></mat-spinner>
        </div>
      </ng-container>

      <ng-template #responseTpl>
        <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
        <ng-container [ngTemplateOutlet]="hasRevisionNote() ? successWithRevision : successActivated"></ng-container>

        <ng-template #successWithRevision>
          <div class="d-flex flex-column w-100 text-center">
            <span>Distributor berhasil dikonfirmasi.</span>
            <span>Catatan aktivasi & Data Distributor dikembalikan ke Regional Head.</span>
          </div>
        </ng-template>

        <ng-template #successActivated>
          <div class="d-flex flex-column w-100 text-center">
            <span>Distributor berhasil diaktivasi.</span>
            <span>Akses login {{ detailHeader.business_name }} :</span>
          </div>

          <div class="group_user_password my-5" *ngIf="dataModalResponse as data">
            <div class="d-flex flex-column align-items-center text-center">
              <strong>{{ data.distributor_email }}</strong>
              <span class="d-flex gap-3">
                <strong>{{ data.distributor_password }}</strong>
                <div ngbPopover="Email & Password berhasil disalin" placement="top" type="button">
                  <span (click)="copyToClipboard()" [inlineSVG]="STRING_CONSTANTS.ICON.IC_COPY"></span>
                </div>
              </span>
            </div>
          </div>

          <app-note-view
            [color]="'secondary'"
            [icon]="STRING_CONSTANTS.ICON.IC_INFO"
            [text]="'Pastikan password berhasil disalin sebelum menutup pop up.'"
            [classNoteView]="'mb-0 mt-6 mx-n8'"
          />
        </ng-template>
      </ng-template>
    </div>
  </app-modal>
</ng-container>
