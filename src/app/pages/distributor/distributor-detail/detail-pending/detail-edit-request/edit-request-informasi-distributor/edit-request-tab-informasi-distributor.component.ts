import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { DistributorService } from '../../../../distributor.service';
import { BehaviorSubject } from 'rxjs';
import { ICardVerificationForm } from '../../../../interfaces/detail-registration.interface';
import { IDetailVerification__DistributorInformation } from '../../../../interfaces/detail-verification.interface';
import {
  businessBodyData,
  distributorDetailBodyData,
  EnumCardVerificationForm,
  EnumCardVerificationSection,
  managerBodyData,
  ownerBodyData,
  scopeBodyData,
} from '../../detail-registration/distributor-data/distributor-data.data';
import { DetailRegistrationDistributorService } from '../../detail-registration/detail-registration.service';

@Component({
  selector: 'app-edit-request-informasi-distributor',
  templateUrl: './edit-request-tab-informasi-distributor.component.html',
  styleUrls: ['./edit-request-tab-informasi-distributor.component.scss'],
})
export class EditRequestTabInformasiDistributorComponent implements OnInit {
  isLoading = new BehaviorSubject(false);
  snapshotParams!: Params;
  detailSubject = new BehaviorSubject({} as IDetailVerification__DistributorInformation);

  //sections data
  scopeSubject = new BehaviorSubject<ICardVerificationForm[]>([]);
  distributorDetailSubject = new BehaviorSubject<ICardVerificationForm[]>([]);
  ownerSubject = new BehaviorSubject<ICardVerificationForm[]>([]);
  managerSubject = new BehaviorSubject<ICardVerificationForm[]>([]);
  businessInfoSubject = new BehaviorSubject<ICardVerificationForm[]>([]);

  constructor(private activeRoute: ActivatedRoute, private registrationService: DetailRegistrationDistributorService, private distributorService: DistributorService) {}

  ngOnInit(): void {
    this.snapshotParams = this.activeRoute.snapshot.params;
    this.getDetailInformasiDistributor();
  }

  getDetailInformasiDistributor() {
    this.isLoading.next(true);
    this.distributorService.getDetailVerificationDistributorInformation(this.snapshotParams.id).subscribe((resp) => {
      if (!resp) return;
      this.detailSubject.next(resp && resp.data);
      this.generateBodyData();
      this.isLoading.next(false);
    });
  }

  generateBodyData() {
    const { scope, distributor_detail, owner, manager, business_information } = this.detailSubject.value;

    const _scope = this.staticMapperDataCard(scope);
    this.scopeSubject.next(this.registrationService.generateBodyData(_scope, scopeBodyData));

    const _distributorDetail = this.staticMapperDataCard(distributor_detail);
    this.distributorDetailSubject.next(this.registrationService.generateBodyData(_distributorDetail, distributorDetailBodyData));

    const _owner = this.staticMapperDataCard(owner);
    this.ownerSubject.next(this.registrationService.generateBodyData(_owner, ownerBodyData));

    const _manager = this.staticMapperDataCard(manager);
    this.managerSubject.next(this.registrationService.generateBodyData(_manager, managerBodyData));

    const _businessInfo = this.staticMapperDataCard(business_information);
    this.businessInfoSubject.next(this.registrationService.generateBodyData(_businessInfo, businessBodyData()));
  }

  staticMapperDataCard<T>(data: T) {
    return {
      revision_note: '',
      revision_data: null,
      current_data: data,
    };
  }

  protected readonly EnumCardVerificationSection = EnumCardVerificationSection;
  protected readonly EnumCardVerificationForm = EnumCardVerificationForm;
}
