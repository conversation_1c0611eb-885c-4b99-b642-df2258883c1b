import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { DistributorService } from '../../../../distributor.service';
import { IDocumentDistributor } from '../../../../interfaces/distributor.interface';
import { ICardDetailBody } from '@models/card.model';
import { UtilitiesService } from '@services/utilities.service';
import { ActivatedRoute, Params } from '@angular/router';
import { RolePrivilegeService } from '@services/role-privilege.service';

@Component({
  selector: 'app-document-distributor',
  templateUrl: './document-distributor.component.html',
  styleUrls: ['./document-distributor.component.scss'],
})
export class DocumentDistributorComponent implements OnInit, OnDestroy {
  snapshotParams!: Params;
  dataValue: BehaviorSubject<IDocumentDistributor> = new BehaviorSubject<IDocumentDistributor>({} as IDocumentDistributor);
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  detailDocumentDistributor: ICardDetailBody[];
  detailDocumentNpwp: ICardDetailBody[];
  detailDocumentActivation: ICardDetailBody[];
  privilegeConfidentialDocuments: Array<any>;

  private unsubscribe: Subscription[] = [];

  constructor(
    private distributorService: DistributorService,
    private utils: UtilitiesService,
    private activatedRoute: ActivatedRoute,
    private rolePrivilegeService: RolePrivilegeService
  ) {
    this.snapshotParams = this.activatedRoute.snapshot.params;
    this.privilegeConfidentialDocuments = this.rolePrivilegeService.checkPrivilegeConfidential('DISTRIBUTOR', 'DETAIL_DISTRIBUTOR', 'TAB_DOCUMENTS');
  }

  ngOnInit(): void {
    this.getData();
    this.initPageInfo();
  }

  getData() {
    const id = this.snapshotParams.id;
    this.distributorService.getDocumentImages(id).subscribe((value) => {
      this.dataValue.next(value);
      this.isLoading.next(false);
    });
  }

  initPageInfo() {
    this.dataValue.subscribe((value) => {
      if (!value) return;
      this.mapDocumentLabelValue(value);
    });
  }

  mapDocumentLabelValue(value: IDocumentDistributor): void {
    const {
      distributor_document: {
        deed_incorporation_document_url,
        deed_amendment_document_url,
        ktp_manager_document_url,
        ktp_owner_document_url,
        place_business_document_url,
        nib_document_url,
      },
      npwp_document: { npwp_document_url, non_pkp_document_url },
      activation_document: { kul_document_url, signing_kul_image_url, integrity_pact_document_url, other_document_url },
    } = value;

    const createDocumentArray = (entries: { label: string; name: string; value: string | null | undefined }[]) => entries.filter((entry) => !!entry.value); // Optional: filter out null/undefined

    this.detailDocumentDistributor = this.utils.privilegeConfidentialDocuments(
      this.privilegeConfidentialDocuments,
      createDocumentArray([
        { label: 'KTP Pemilik Usaha :', name: 'CARD_PIC_KTP_PEMILIK', value: ktp_owner_document_url },
        { label: 'KTP Pengelola Usaha :', name: 'CARD_PIC_KTP_PENGELOLA', value: ktp_manager_document_url },
        { label: 'Akta Pendirian:', name: 'CARD_PIC_AKTA_PENDIRIAN', value: deed_incorporation_document_url },
        { label: 'Akta Perubahan :', name: 'CARD_PIC_AKTA_PERUBAHAN', value: deed_amendment_document_url },
        { label: 'NIB :', name: 'CARD_PIC_NIB', value: nib_document_url },
        { label: 'Foto Tempat Usaha :', name: 'CARD_PIC_TEMPAT_USAHA', value: place_business_document_url },
      ])
    );

    this.detailDocumentNpwp = this.utils.privilegeConfidentialDocuments(
      this.privilegeConfidentialDocuments,
      createDocumentArray([
        { label: 'NPWP :', name: 'CARD_PIC_NPWP', value: npwp_document_url },
        { label: 'Surat Pernyataan non PKP :', name: 'CARD_PIC_NON_PKP', value: non_pkp_document_url },
      ])
    );

    this.detailDocumentActivation = createDocumentArray([
      { label: 'KUL :', name: 'CARD_PIC_KUL', value: kul_document_url },
      { label: 'Foto TTD KUL :', name: 'CARD_PIC_SIGNING_KUL', value: signing_kul_image_url },
      { label: 'Pakta Integritas :', name: 'CARD_PIC_INTEGRITY_PACT', value: integrity_pact_document_url },
      { label: 'Lainnya :', name: 'CARD_PIC_OTHER', value: other_document_url },
    ]);
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
