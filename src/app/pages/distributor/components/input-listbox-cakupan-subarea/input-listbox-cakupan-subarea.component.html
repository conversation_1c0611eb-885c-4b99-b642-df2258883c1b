<div class="d-flex flex-wrap justify-content-between align-items-start mb-6">
  <label class="col-12 col-lg-4 col-form-label" [class.required]="required">{{ label }}</label>
  <div class="col-12 col-lg-8">
    <div class="mb-2" *ngIf="chipsListData && chipsListData.length">
      <app-chips-legacy [chipsList]="chipsListData" (dataOutput)="handleOutputChips($event)"></app-chips-legacy>
    </div>

    <button class="btn btn-outline px-4" type="button" mat-button color="primary" (click)="handleOpenDialog()" [disabled]="loading.value">
      <span class="svg-icon svg-icon-2" [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS"></span>
      <span class="text-primary pe-2">
        <ng-container *ngIf="loading.value; else defaultCtaLabel">
          <span>fetching data...</span>
          <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
        </ng-container>
        <ng-template #defaultCtaLabel>
          {{ ctaLabelOpenDialog }}
        </ng-template>
      </span>
    </button>
  </div>
</div>

<app-modal-form #modalForm [modalConfig]="modalFormConfig">
  <!--  SELECTED CHIPS -->
  <div class="mb-2" *ngIf="listSelectedItems.length">
    <app-chips-legacy [chipsList]="listSelectedItems" (dataOutput)="handleOutputChips($event)"></app-chips-legacy>
  </div>

  <!--  NOTE VIEW INFO -->
  <app-note-view *ngIf="isAllListSelected()" color="info" [text]="StaticText.NOTEVIEW_INFO_COVERAGE" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"></app-note-view>

  <!--  CHECKBOX LIST -->
  <ng-template #listItemsTpl>
    <div class="multiselect-list mh-200px overflow-scroll mb-4">
      <ng-container *ngFor="let item of listItemsBank; index as index; let last = last">
        <div class="form-check my-6 text-capitalize">
          <input (change)="handleChangeSelection()" [(ngModel)]="item.selected" class="form-check-input cursor-pointer" id="flexCheckDefault-{{ item.id }}" type="checkbox" />
          <label class="form-check-label cursor-pointer ms-2 text-gray-900 text-capitalize" for="flexCheckDefault-{{ item.id }}">
            {{ item.name | lowercase }}
          </label>
        </div>
      </ng-container>
    </div>
  </ng-template>

  <ng-container *ngIf="!listItemsBank.length && (loading | async); else listItemsTpl">
    <ng-template [ngTemplateOutlet]="listLoader"></ng-template>
  </ng-container>

  <!--  CTA: ADD ITEMS -->
  <div class="d-flex justify-content-end">
    <button class="btn btn-primary" (click)="handleAddChips()" [disabled]="isAllListSelected() || !listSelectedItems.length" type="button">
      <div class="d-flex align-items-center">
        <span>{{ ctaLabelAdd }}</span>
        <span *ngIf="loading.value" class="spinner-border spinner-border-sm align-middle ms-2"></span>
      </div>
    </button>
  </div>

  <ng-template #listLoader>
    <app-input-listbox-loader></app-input-listbox-loader>
  </ng-template>
</app-modal-form>
