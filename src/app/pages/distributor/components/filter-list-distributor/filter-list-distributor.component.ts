import { Component, HostBinding, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { BehaviorSubject, shareReplay, Subscription } from 'rxjs';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InputSelectInterface } from '@shared/components/form/input-select/input-select.interface';
import { DistributorService } from '../../distributor.service';
import { FilterService } from '@services/filter.service';
import {
  InputSelectAutocompleteComponent,
} from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.component';
import { IDistributorArea } from '../../interfaces/distributor.interface';
import {
  IInputSelectOptions,
  ISelectDistributorArea,
} from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.interface';
import { map } from 'rxjs/operators';
import { BaseResponse } from '@shared/base/base-response';

@Component({
  selector: 'app-filter-list-distributor',
  templateUrl: './filter-list-distributor.component.html',
  styleUrls: ['./filter-list-distributor.component.scss'],
})
export class FilterListDistributorComponent implements OnInit, OnDestroy {
  @HostBinding('class') class = 'd-flex w-100 justify-content-between';
  @Input() finishLoadingSubject = new BehaviorSubject(true);

  @Input() searchInputPlaceholder: string = 'Cari Nama Distributor';
  @Input() searchInputValue: string = '';

  @Input() filterInputActivated = false;
  @Input() filterInputOpened = false;

  filterForm: FormGroup;

  listDistributorType: InputSelectInterface[] = [];

  @ViewChild('inputSelectArea') inputSelectArea: InputSelectAutocompleteComponent;

  private unsubscribe: Subscription[] = [];

  constructor(
    private distributorService: DistributorService,
    private formBuilder: FormBuilder,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    public filterService: FilterService,
  ) {
  }

  get AreaFilterControl() {
    return <FormControl>this.filterForm.get('area_id');
  }

  ngOnInit(): void {
    this.initFilter();
  }

  getListFilter() {
    this.getListDistributorType();
    this.loadDistributorAreaList();
  }

  initFilter() {
    this.filterForm = this.formBuilder.group({
      type: [''],
      area_manager_id: [''],
      area_id: [''],
    });
  }

  getListDistributorType() {
    if (!!this.listDistributorType.length) return;
    const _listSubs = this.distributorService
      .getListDistributorType()
      .pipe(shareReplay())
      .subscribe((res) => {
        if (!res) {
          return;
        }

        this.listDistributorType = res;
      });

    this.unsubscribe.push(_listSubs);
  }


  toggleOpenFilter = () => {
    this.filterInputOpened = !this.filterInputOpened;
    if (this.filterInputOpened) this.getListFilter();
  };

  handleSearchAction(e: string) {
    const snapshot = this.activatedRoute.snapshot;
    const dataParam = { ...snapshot.queryParams };

    if (e) {
      delete dataParam.page;
      return this.router.navigate([], {
        queryParams: {
          ...dataParam,
          string_filter: '' || e,
        },
      });
    } else {
      delete dataParam.string_filter;
      return this.router.navigate([], { queryParams: dataParam });
    }
  }

  handleResetFilter() {
    this.filterInputActivated = false;
    this.filterInputOpened = false;
    this.filterForm.setValue({
      type: '',
      area_manager_id: '',
      area_id: '',
    });

    this.inputSelectArea.clearSearchInput();

    const snapshot = this.activatedRoute.snapshot;
    const dataParam = { ...snapshot.queryParams };
    delete dataParam.type;
    delete dataParam.area_manager_id;
    delete dataParam.area_id;

    this.router.navigate([], { queryParams: dataParam }).then();
  }

  handleSubmitFilter() {
    Object.keys(this.filterForm.value).forEach((_key) => {
      if (this.filterForm.value[_key] === '') {
        delete this.filterForm.value[_key];
      }
    });

    if (!Object.keys(this.filterForm.value).length) {
      return;
    }

    this.filterInputActivated = true;
    this.filterInputOpened = false;

    const snapshot = this.activatedRoute.snapshot;
    const dataParam = { ...snapshot.queryParams };
    delete dataParam.page;

    this.router.navigate([], { queryParams: { ...dataParam, ...this.filterForm.value } }).then(() => null);
  }

  loadDistributorAreaList() {
    this.inputSelectArea.fetchDataFn = this.getListDistributorArea.bind(this);
    this.inputSelectArea.fetchData();
  }

  getListDistributorArea = () =>
    this.distributorService.getListDistributorArea().pipe(
      map((resp) => {
        const _resp = <BaseResponse<IInputSelectOptions[]>>(<unknown>resp);

        if (resp) {
          _resp.data = this.mapInputSelectDistributorArea(resp.data);
          return _resp;
        }

        return _resp;
      }),
    );

  mapInputSelectDistributorArea(data: IDistributorArea[]) {
    return data.map((item) => {
      const inputSelector = new ISelectDistributorArea();
      inputSelector.setSelector(item);
      return inputSelector as IInputSelectOptions;
    });
  }

  onSelectedArea = (e: IInputSelectOptions) => this.AreaFilterControl.patchValue(e.value);

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
