<ng-container *ngIf="userDetail$ | async as detail">
  <app-modal #modalSelectScope [modalConfig]="modalConfigSelectScope">
    <app-note-view *ngIf="isEdit.value && detail.status_enum === 'ACTIVE'" [color]="'info'" [extraContent]="true" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION">
      <div>Cakupan {{ generateLabelScope() }} {{ detail.name }} ({{ detail.role_enum_value }}) saat ini:</div>
      <div>{{ generateAreaWarehouseUser() }}</div>
    </app-note-view>

    <ng-container *ngIf="isShowScope(detail, 'AREA')">
      <app-input-select-scope
        #selectScopeArea
        (selectedValues)="onSelectScope($event)"
        [endpointUrl]="API.AREA.GET_LIST_SELECT_SCOPE"
        [icon]="STRING_CONSTANTS.ICON.IC_LOCATION_PIN"
        [isEdit]="isEdit"
        [isMultiple]="isMultipleInputArea"
        [label]="'Pilih Area'"
        [placeholder]="'Silahkan pilih cakupan Area'"
        [selectedValue]="selectedScopeValue('AREA')"
        [type]="'AREA'"
        [enumRole]="detail.role_enum"
        [usePagination]="false"
      />
    </ng-container>

    <ng-container *ngIf="isShowScope(detail, 'SUBAREA')">
      <hr class="my-6" />
      <!-- select subarea rely on area id -->
      <app-input-select-scope
        #selectScopeSubarea
        (selectedValues)="onSelectScope($event)"
        [areaId]="areaId"
        [endpointUrl]="API.AREA.GET_LIST_SUB_AREA"
        [icon]="STRING_CONSTANTS.ICON.IC_LOCATION"
        [isEdit]="isEdit"
        [enumRole]="detail.role_enum"
        [isMultiple]="isMultipleInputSubArea"
        [placeholder]="'Silahkan pilih cakupan Sub area'"
        [selectedValue]="selectedScope.subAreaID"
        [type]="'SUBAREA'"
        [usePagination]="false"
        label="Pilih Sub area"
      />
    </ng-container>

    <ng-container *ngIf="isShowScope(detail, 'WAREHOUSE')">
      <hr class="my-6" />
      <!-- select gudang rely on area id -->
      <app-input-select-scope
        #selectScopeWarehouse
        (selectedValues)="onSelectScope($event)"
        [areaId]="areaId"
        [endpointUrl]="API.USER.LIST_WAREHOUSE"
        [icon]="STRING_CONSTANTS.ICON.IC_WAREHOUSE"
        [isEdit]="isEdit"
        [placeholder]="'Silahkan pilih cakupan Gudang'"
        [selectedValue]="selectedScope.warehouseID"
        [type]="'WAREHOUSE'"
        [usePagination]="false"
        label="Pilih Gudang"
      />
    </ng-container>

    <ng-container *ngIf="!isLoading.value && detail.status_enum === 'ACTIVE' && showSelectRole()">
      <div class="mt-4">
        <app-input-select-scope-role
          #selectScopeRole
          (selectedValues)="onSelectScopeRole($event)"
          [areaValue]="areaWarehouseValue"
          [endpointUrl]="API.USER.LIST_ASSIGN_USER"
          [enumRole]="detail.role_enum"
          [icon]="STRING_CONSTANTS.ICON.IC_REPLACEMENT_ACCOUNT"
          [isOptional]="validateOptionalInput()"
          [label]="generateLabelRole()"
          [roleValue]="detail.role_enum_value"
          [placeholder]="'Silahkan pilih ' + detail.role_enum_value + ' pengganti'"
          [usePagination]="false"
        />
      </div>
    </ng-container>
  </app-modal>
</ng-container>

<app-modal #modalResponse [callbackSubmit]="closeAssignModalResponse" [modalConfig]="modalConfigResponse">
  <ng-container [ngTemplateOutlet]="Object.keys(responseAssignUser).length ? assignTpl : unassignTpl" />

  <ng-template #assignTpl>
    <div *ngIf="Object.keys(responseAssignUser).length" class="d-flex flex-column justify-content-center align-items-center">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT" class="mb-6"></span>
      <div class="text-center">{{ responseAssignUser.message }}</div>
      <div class="group_user_password">
        <div *ngIf="responseAssignUser.password" class="mt-5 d-flex w-100 justify-content-center on_copy_password align-items-center">
          <strong class="text-center">{{ responseAssignUser.password }}</strong>
          <div class="d-flex" ngbPopover="Password berhasil di copy" placement="top" type="button">
            <span (click)="copyToClipboard(responseAssignUser.password)" [inlineSVG]="STRING_CONSTANTS.ICON.IC_COPY" class="cursor-pointer"></span>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #unassignTpl>
    <ng-container *ngIf="Object.keys(responseUnassignUser).length">
      <div class="d-flex flex-column justify-content-center align-items-center">
        <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT" class="mb-6"></span>
        <div class="text-center">Cakupan {{ detail.name }} ({{ detail.role_enum_value }}) diperbarui menjadi {{ responseUnassignUser.scope_list.join(', ') }}</div>
      </div>
      <app-note-view *ngIf="responseUnassignUser.replacement.length" [color]="'info'" [classNoteView]="'my-4'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [extraContent]="true">
        <ul>
          <li *ngFor="let item of responseUnassignUser.replacement">{{ item.scope }} Ditugaskan ke {{ item.user }} ({{ detail.role_enum_value }})</li>
        </ul>
      </app-note-view>
    </ng-container>
  </ng-template>
</app-modal>
