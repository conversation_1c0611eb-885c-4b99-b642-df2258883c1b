<!-- TAB GROUP -->
<mat-tab-group mat-stretch-tabs="false" animationDuration="0" disableRipple class="square" [selectedIndex]="selectedTabIndex" (selectedTabChange)="handleTabChange($event)">
  <mat-tab *ngFor="let item of roleTabMenu" label="{{ item.enum }}">
    <ng-template matTabLabel>
      <span class="text-capitalize">{{ item.name }}</span>
    </ng-template>

    <ng-template matTabContent>
      <ng-container [ngSwitch]="item.enum">
        <div *ngSwitchCase="utilitiesService.mapKeyToString(PLATFORM_TYPE, 'BO')">
          <app-list-role-backoffice [baseDataSource]="baseDataSource"></app-list-role-backoffice>
        </div>

        <div *ngSwitchCase="utilitiesService.mapKeyToString(PLATFORM_TYPE, 'MMPLUS')">
          <app-list-role-maxximarketing [baseDataSource]="baseDataSource"></app-list-role-maxximarketing>
        </div>
      </ng-container>
    </ng-template>
  </mat-tab>
</mat-tab-group>
