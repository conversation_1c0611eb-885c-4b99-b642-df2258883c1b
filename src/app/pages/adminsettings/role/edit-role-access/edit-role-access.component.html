<div class="container px-0">
  <div class="row">
    <!--    {{ currentRolePrivilege$ | async | json }}-->

    <div class="col-12 mb-4">
      <h3 class="fw-bold">{{ roleSubject.value.enum_string }}</h3>
    </div>

    <!--  ASIDE MENU -->
    <div class="col-lg-3 mb-4 mb-lg-0">
      <app-card [cardClasses]="'shadow-xs min-h-200px'" [cardBodyClasses]="'px-7 py-5'">
        <ng-container cardBody>
          <p class="fw-bold font-16 mb-5">Menu</p>
          <div class="aside-menu">
            <ng-container *ngIf="!(privileges$ | async)?.length">
              <ng-template [ngTemplateOutlet]="loaderSideMenuItem"></ng-template>
            </ng-container>

            <div class="menu-item mb-3" *ngFor="let menu of privileges$ | async" [class.active]="menu.active">
              <div class="menu-link px-0" (click)="handleSelectPrivilege(menu.name)" [ngClass]="menu.active ? 'text-gray-900' : 'text-gray-700'">
                <span class="menu-icon">
                  <span class="svg-icon svg-icon-2" [inlineSVG]="'/assets/media/icons/' + menu.icon"></span>
                </span>
                <div class="menu-title d-flex">
                  <span>{{ menu.displayName }}</span>
                  <span *ngIf="menu.ticked" class="svg-icon svg-icon-2 ms-auto" [inlineSVG]="STRING_CONSTANTS.ICON.IC_BULLET_TICK_GREEN"></span>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </app-card>
    </div>

    <!--  CONTENT -->
    <div class="col">
      <app-card-privilege-checkbox
        #appPrivilegeCheckbox
        [role]="roleSubject.value.enum_value"
        [appType]="appTypeSubject.value"
        [privileges]="selectedPrivilege.value"
        (checkedPrivilegeList)="handleEmittedPrivilegeList($event)"
      ></app-card-privilege-checkbox>
      <div class="d-flex justify-content-end">
        <button type="submit" class="btn btn-primary min-w-150px" (click)="modalConfirmSave.open()" [disabled]="!data.length">Save</button>
      </div>
    </div>
  </div>
</div>

<app-modal #modalConfirmSave [modalConfig]="modalConfig" [modalOptions]="{ size: 'md' }">
  <div class="text-center">
    Hak akses dari <span class="fw-bold">{{ roleSubject.value.enum_string }}</span> akan berubah. Apakah anda yakin?
  </div>
  <div class="button-group mt-12 d-flex flex-nowrap align-items-center">
    <button type="button" class="btn btn-outline btn-outline-secondary w-100 me-lg-3" (click)="modalConfirmSave.dismiss()" [disabled]="isLoadingSubject.value">
      {{ modalConfig.closeButtonLabel }}
    </button>
    <button type="button" class="btn w-100 btn-primary" (click)="handleSaveResponse()" [disabled]="isLoadingSubject.value">
      <span class="indicator-progress me-2" [style.display]="'inline-block'" *ngIf="isLoadingSubject.value">
        <span class="spinner-border spinner-border-sm align-middle"></span>
      </span>
      <span>
        {{ modalConfig.dismissButtonLabel }}
      </span>
    </button>
  </div>
</app-modal>

<ng-template #loaderSideMenuItem>
  <div class="menu-item">
    <div class="menu-link px-0">
      <span class="menu-icon">
        <app-skeleton-text [type]="'icon'" [width]="28" [height]="28"></app-skeleton-text>
      </span>
      <span class="menu-title">
        <app-skeleton-text [type]="'text'" [width]="150"></app-skeleton-text>
      </span>
    </div>
  </div>

  <div class="menu-item">
    <div class="menu-link px-0">
      <span class="menu-icon">
        <app-skeleton-text [type]="'icon'" [width]="28" [height]="28"></app-skeleton-text>
      </span>
      <span class="menu-title">
        <app-skeleton-text [type]="'text'" [width]="150"></app-skeleton-text>
      </span>
    </div>
  </div>

  <div class="menu-item">
    <div class="menu-link px-0">
      <span class="menu-icon">
        <app-skeleton-text [type]="'icon'" [width]="28" [height]="28"></app-skeleton-text>
      </span>
      <span class="menu-title">
        <app-skeleton-text [type]="'text'" [width]="150"></app-skeleton-text>
      </span>
    </div>
  </div>
</ng-template>

<app-modal #modalSaveResponse [modalConfig]="modalConfigSaveResponse" [modalOptions]="{ size: 'md' }">
  <div class="d-flex flex-column justify-content-center align-items-center mb-n10">
    <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
    <div class="mt-4">
      Hak akses dari <span class="fw-bold">{{ roleSubject.value.enum_string }}</span> berhasil diperbarui.
    </div>
  </div>
</app-modal>
