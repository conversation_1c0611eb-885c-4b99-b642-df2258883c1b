<app-input-search [isFinishLoadingSubject]="finishLoadingSubject" [value]="searchInputValue" (actionFilter)="actionSearch($event)" placeholder="Silahkan cari nama/role" />
<div class="ms-auto position-relative">
  <div class="d-flex align-items-center justify-content-center">
    <button *ngIf="showAddTeamCTA" (click)="onAddTeam()" class="btn btn-primary" color="primary" mat-raised-button>Tambah Anggota Tim</button>
    <div class="ms-3">
      <app-filter-table
        [isOpenFilter]="filterInputOpened"
        [isActiveFilter]="filterInputActivated"
        [isFinishLoadingSubject]="finishLoadingSubject"
        [resetLabel]="'Reset Filter'"
        (actionClick)="toggleOpenFilter()"
        (actionReset)="actionResetFilter()"
      >
        <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
          <div class="px-7 py-5">
            <div class="d-flex align-items-center justify-content-between">
              <div class="fs-4 text-dark fw-bold">Filter</div>
              <div>
                <span (click)="toggleOpenFilter()" [inlineSVG]="STRING_CONSTANTS.ICON.IC_CLOSE_MODAL" class="svg-icon svg-icon2 cursor-pointer"></span>
              </div>
            </div>
          </div>

          <div class="separator border-gray-300"></div>

          <form class="form w-100" [formGroup]="filterForm" (ngSubmit)="actionSubmitFilter()">
            <div class="px-7 py-5">
              <div class="mb-10">
                <label class="form-label fs-5 mb-4 d-block">Role</label>
                <ng-container [ngTemplateOutlet]="(isLoading | async) ? loaderTpl : filterPillsTpl"></ng-container>

                <ng-template #filterPillsTpl>
                  <div class="filter-pills">
                    <mat-chip-listbox #filterPillsBox multiple (change)="actionStatusChange($event)">
                      <ng-container formArrayName="role">
                        <mat-chip-option *ngFor="let item of filterPills | async" [value]="item.enum_value" class="chips animation animation-fade-in">
                          <div class="d-flex align-items-center justify-content-between">
                            <span>{{ item.enum_string }}</span>
                            <span class="custom-x-icon svg-icon cursor-pointer" [inlineSVG]="STRING_CONSTANTS.ICON.IC_X"></span>
                          </div>
                        </mat-chip-option>
                      </ng-container>
                    </mat-chip-listbox>
                  </div>
                </ng-template>

                <ng-template #loaderTpl>
                  <span>loading...</span>
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </ng-template>
              </div>

              <div class="d-flex justify-content-end mb-5">
                <button class="btn btn-outline me-4 btn-sm" mat-stroked-button type="reset" (click)="actionResetFilter()" [disabled]="!RoleControl.value">
                  <span class="text-primary">Reset</span>
                </button>
                <button class="btn btn-primary btn-sm" color="primary" mat-raised-button type="submit" [disabled]="!RoleControl.value">
                  <span class="text-white">Terapkan</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </app-filter-table>
    </div>
  </div>
</div>
