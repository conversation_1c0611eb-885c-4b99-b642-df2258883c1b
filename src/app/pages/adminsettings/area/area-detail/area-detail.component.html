<!-- Action Toolbar -->
<div *ngIf="privilegeEdit.value" class="d-flex align-items-center position-absolute top-0 end-0 m-9 mt-6">
  <!--begin::Action menu-->
  <a href="#" class="btn btn-outline btn-outline-primary ps-7 mb-2" data-kt-menu-trigger="click" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end"
    >Actions
    <!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
    <span class="svg-icon svg-icon-2 me-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
          fill="currentColor"
        />
      </svg>
    </span>
    <!--end::Svg Icon-->
  </a>

  <!--begin::Menu-->
  <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold py-4 w-250px fs-6" data-kt-menu="true">
    <!--begin::Menu item-->
    <div class="menu-item px-5">
      <a class="menu-link" *ngIf="privilegeEdit.value" (click)="handleAction(area, areaActionsEnum.EDIT_MODAL_AREA)">
        <span inlineSVG="./assets/media/icons/ic_edit.svg" class="svg-icon svg-icon-2 me-3"></span>
        <span>Edit Data</span>
      </a>
    </div>
    <div class="menu-item px-5">
      <a class="menu-link" (click)="handleSetTarget()">
        <span class="svg-icon svg-icon-2 me-3" inlineSVG="./assets/media/icons/ic_print.svg"></span>
        <span>Setting Target</span>
      </a>
    </div>
    <!--end::Menu item-->
  </div>
</div>
<!-- Action Toolbar -->

<app-note-view
  *ngIf="area && !area.warehouse_list.length"
  [icon]="STRING_CONSTANTS.ICON.IC_DANGER_NONE"
  [color]="'warning-secondary'"
  [text]="'Area belum diassign ke gudang, silahkan assign gudang.'"
/>

<app-card *ngIf="area$ | async; else loadingArea">
  <div cardBody class="d-flex flex-wrap flex-sm-nowrap">
    <div class="flex-grow-1">
      <div class="d-flex flex-column align-items-start flex-wrap mb-5">
        <h2 class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">{{ area.area_name }}</h2>
        <span class="text-gray-700 d-block mb-2">Kode Area: {{ area.area_code }}</span>
      </div>

      <div class="row text-gray-700">
        <!-- deskripsi -->
        <div class="col-12 col-md">
          <span class="text-gray-700 d-block mb-2">Deskripsi</span>
          <span class="fw-bold fs-5 text-dark">{{ area.desc }}</span>
        </div>

        <!-- cakupan provinsi -->
        <div class="col-12 col-md" *ngIf="area.province_list">
          <span class="text-gray-700 d-block mb-2">Provinsi</span>
          <div class="fw-bold fs-5 text-dark block-ellipsis-customs">{{ renderStringList(area.province_list, 'name', 6) }}</div>
          <div *ngIf="area.province_list.length > 5" class="mt-2">
            <span class="cursor-pointer fw-bold" style="color: #5186cf" (click)="handleAction(area, areaActionsEnum.INFO_MODAL_AREA)">Lihat detail</span>
          </div>
        </div>

        <!-- gudang -->
        <div class="col-12 col-md">
          <span class="text-gray-700 d-block mb-2">Gudang</span>
          <div class="fw-bold fs-5 text-dark block-ellipsis-customs">
            {{ area.warehouse_list.length ? renderStringList(area.warehouse_list, 'name', 6) : '-' }}
          </div>
          <div class="mt-2" *ngIf="area.warehouse_list.length > 5">
            <span class="cursor-pointer fw-bold" style="color: #5186cf" (click)="handleAction(area, areaActionsEnum.INFO_MODAL_GUDANG)">Lihat detail</span>
          </div>
        </div>

        <!-- anggota tim -->
        <div class="col-12 col-md">
          <span class="text-gray-700 d-block mb-2">Anggota Tim</span>
          <div class="fw-bold fs-5 text-dark">{{ area.total_team ?? '-' }}</div>
          <div class="mt-2">
            <!-- go to detail team page -->
            <span class="cursor-pointer fw-bold text-info" (click)="onViewDetailTeam()">Lihat detail</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-card>

<ng-template #loadingArea>
  <app-fullscreen-loader />
</ng-template>

<!-- begin: Card table list of Sub Area -->
<app-card *ngIf="privilegeListSubArea.value" [cardClasses]="'mt-10'" [header]="true" [cardBodyClasses]="'pt-2'" [cardHeaderTitle]="'Sub Area (' + getSubAreaTotal() + ')'">
  <!-- begin::Card Header -->
  <ng-container cardHeader>
    <div class="card-toolbar d-flex align-items-center my-auto w-100 mt-8">
      <app-input-search
        [placeholder]="'Silahkan cari nama Sub Area'"
        [value]="string_filter"
        (actionFilter)="onSearch($event)"
        [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
      />

      <div class="ms-auto" *ngIf="PrivilegeAddSubArea">
        <button *ngIf="finishLoading$ | async; else loaderButton" type="button" class="btn btn-primary" (click)="handleAction(area.area_id, areaActionsEnum.ROUTE_ADD_SUBAREA)">
          Tambah Sub Area
        </button>
      </div>
      <ng-template #loaderButton>
        <ngx-skeleton-loader count="1" [theme]="{ 'border-radius': '5', height: '40px', width: '100px' }"></ngx-skeleton-loader>
      </ng-template>
    </div>
  </ng-container>

  <!-- begin::Card Body -->
  <ng-container cardBody>
    <ng-container
      [ngTemplateOutlet]="!string_filter && baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0 ? emptyStateSubAreaTpl : elseBlock"
    ></ng-container>

    <ng-template #elseBlock>
      <ng-container
        [ngTemplateOutlet]="string_filter && baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0 ? emptyStateSubAreaTpl : elseFilterBlock"
      ></ng-container>

      <ng-template #elseFilterBlock>
        <div class="table-responsive">
          <table mat-table class="table w-100 gy-5 table-row-bordered" [dataSource]="baseDatasource">
            <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
              <!-- COLUMN HEADER -->
              <th
                mat-header-cell
                *matHeaderCellDef
                class="px-3"
                [class.min-w-200px]="tableColumn.key !== 'actions'"
                [class.w-200px]="tableColumn.key === 'code'"
                [ngClass]="{
                  'min-w-70px text-center': tableColumn.key === 'actions',
                  'w-lg-400px': tableColumn.key === 'regency_list' || tableColumn.key === 'desc'
                }"
              >
                <span *ngIf="baseDatasource.isFinishLoadingSubject.value">
                  {{ tableColumn.title }}
                </span>
                <div *ngIf="!baseDatasource.isFinishLoadingSubject.value">
                  <app-table-content></app-table-content>
                </div>
              </th>
              <!-- COLUMN DATA -->
              <td mat-cell *matCellDef="let element" class="px-3">
                <ng-container [ngSwitch]="tableColumn.key">
                  <div *ngSwitchCase="'regency_list'">
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <span class="block-ellipsis-customs">{{ renderStringList(element[tableColumn.key], 'name') }}</span>
                    </app-table-content>
                  </div>

                  <div *ngSwitchCase="'actions'" class="text-center">
                    <ng-container>
                      <div style="display: flex; gap: 20px; justify-content: center">
                        <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                          <span
                            [inlineSVG]="'./assets/media/icons/ic_task.svg'"
                            class="svg-icon svg-icon-2 cursor-pointer mx-2"
                            (click)="handleAction(element, areaActionsEnum.INFO_MODAL_SUBAREA)"
                          ></span>
                        </app-table-content>
                        <ng-container *ngIf="hasPrivilegeCTA('CTA_EDIT')">
                          <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                            <span
                              [inlineSVG]="'./assets/media/icons/ic_edit.svg'"
                              class="svg-icon svg-icon-2 cursor-pointer mx-2"
                              (click)="handleAction(element, areaActionsEnum.EDIT_MODAL_SUBAREA)"
                            ></span>
                          </app-table-content>
                        </ng-container>
                      </div>
                    </ng-container>
                  </div>

                  <div *ngSwitchDefault>
                    <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <span class="block-ellipsis-customs">{{ element[tableColumn.key] ?? '-' }}</span>
                    </app-table-content>
                  </div>
                </ng-container>
              </td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
        <div class="d-flex justify-content-between py-4">
          <app-mai-material-bottom-table
            class="w-100"
            [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
            [baseDataTableComponent]="baseDatasource"
            (changePage)="changePageEvent($event)"
          ></app-mai-material-bottom-table>
        </div>
      </ng-template>
    </ng-template>
  </ng-container>
  <!-- end::Card Body -->
</app-card>

<ng-template #emptyStateSubAreaTpl>
  <app-card-empty text="Belum terdapat data sub area." icon="{{ iconNone }}" />
</ng-template>
<!-- end: Card table list of Sub Area -->

<app-modal *ngIf="act === areaActionsEnum.EDIT_MODAL_AREA" #modalConfirmEditArea [modalConfig]="modalConfigEditArea">
  <div class="text-center">
    Apakah anda yakin akan merubah data area <span class="fw-bold">{{ area_data.area_name }}</span
    >? Data yang di edit akan berpengaruh kepada Distributor dan Retailer yang sudah terdaftar.
  </div>
</app-modal>

<app-modal *ngIf="act === areaActionsEnum.EDIT_MODAL_SUBAREA" #modalConfirmEditSubArea [modalConfig]="modalConfigEditSubArea">
  <div class="text-center">
    Apakah anda yakin akan merubah data sub area <span class="fw-bold">{{ area_data.name }}</span
    >? Data yang di edit akan berpengaruh kepada Distributor dan Retailer yang sudah terdaftar.
  </div>
</app-modal>

<app-modal *ngIf="act === areaActionsEnum.INFO_MODAL_SUBAREA" #modalInfoSubArea [modalConfig]="modalConfigInfo">
  <div class="m-n5" *ngIf="area_data">
    <h4 class="fw-bold fs-2 mb-5">{{ area_data.name }}</h4>
    <div class="d-flex">
      <div class="min-w-125px">
        <span class="d-block text-gray-700 fs-7 mb-2 fw-lighter">Kode Sub Area</span>
        <span class="fw-bold">{{ area_data.code }}</span>
      </div>
      <div class="min-w-125px">
        <span class="d-block text-gray-700 fs-7 mb-2 fw-lighter">Jumlah Kota/Kabupaten</span>
        <span class="fw-bold">{{ area_data.regency_list.length }}</span>
      </div>
    </div>
    <hr class="my-6" />
    <p class="text-gray-700 mb-4">List Kota/Kabupaten</p>
    <div class="mh-200px overflow-auto">
      <ul class="list-unstyled" *ngIf="area_data.regency_list">
        <li class="mb-2" *ngFor="let item of area_data.regency_list">{{ item.name }}</li>
      </ul>
    </div>
  </div>
</app-modal>

<app-modal *ngIf="act === areaActionsEnum.INFO_MODAL_AREA" #modalInfoArea [modalConfig]="modalConfigInfo" [modalOptions]="{ size: 'lg' }">
  <div class="m-n5">
    <h4 class="fw-bold fs-2 mb-5">Cakupan Provinsi Area {{ area_data.area_name }}</h4>
    <hr class="my-6" />
    <div class="mh-200px overflow-auto">
      <ul class="list-unstyled">
        <li class="mb-3" *ngFor="let item of area_data.province_list">
          {{ item.name }}
        </li>
      </ul>
    </div>
  </div>
</app-modal>

<app-modal *ngIf="act === areaActionsEnum.INFO_MODAL_GUDANG" #modalInfoGudang [modalConfig]="modalConfigInfo" [modalOptions]="{ size: 'lg' }">
  <div class="m-n5">
    <h4 class="fw-bold fs-2 mb-5">Gudang Area {{ area_data['area_name'] }}</h4>
    <hr class="my-6" />
    <div class="mh-200px overflow-auto">
      <ul class="list-unstyled">
        <li class="mb-3" *ngFor="let item of area_data['warehouse_list']">
          <span>{{ item.name }}</span>
          <span class="d-block">{{ item.address }}</span>
        </li>
      </ul>
    </div>
  </div>
</app-modal>
