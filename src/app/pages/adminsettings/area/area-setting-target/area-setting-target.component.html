<div style="display: grid; gap: 24px">
  <app-card>
    <ng-container cardBody>
      <div class="card-toolbar d-flex align-items-center my-auto w-100">
        <h1>{{ title }}</h1>
        <div class="ms-auto">
          <button type="button" class="btn btn-primary" (click)="handleDownload()">Download</button>
        </div>
      </div>
    </ng-container>
  </app-card>
  <app-card>
    <ng-container cardBody>
      <app-area-setting-target-form [cbdID]="areaID" (handleCancel)="handleModalCancel()" (periode)="onPeriodeReceived($event)"> </app-area-setting-target-form>
    </ng-container>
  </app-card>
</div>

<app-modal #modalDataDownload [modalConfig]="modalDataDownloadConfig" [modalOptions]="modalOptionDownloadConfig">
  <div class="modal-container">
    <ng-container *ngFor="let modal of modals; let i = index">
      <div class="text-center mt-8">
        <p class="mb-0 required">Download {{ modal.title }}</p>
        <button (click)="getDownloadCSV(modal.title, modal.url, modal.type)" class="btn">
          <span [inlineSVG]="assetIcon.IC_DOWNLOAD_FILE" class="mx-3"></span>
        </button>
      </div>
    </ng-container>
  </div>
</app-modal>

<app-modal #modalConfirmCancel [modalConfig]="modalConfirmCancelConfig">
  <div class="text-center mt-8">
    <p class="mb-0">Apakah anda yakin untuk keluar dari halaman tambah setting target? Data yang sudah diinput akan hilang.</p>
  </div>
</app-modal>
