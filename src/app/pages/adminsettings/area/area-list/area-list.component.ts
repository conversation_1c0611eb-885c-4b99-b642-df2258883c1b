import { Component, OnInit, ViewChild } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { ActivatedRoute, Router } from '@angular/router';
import { PageInfoService, PageLink } from '@metronic/layout/core/page-info.service';
import { UtilitiesService } from '@services/utilities.service';
import { BaseComponent } from '@shared/base/base.component';
import { BaseDatasource } from '@shared/base/base.datasource';
import { TableColumn } from '@shared/interface/table.interface';
import { IAreaRoot } from '../area.interface';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { UrlUtilsService } from '@utils/url-utils.service';
import { API } from '@config/constants/api.constant';
import { BehaviorSubject, Observable } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { FilterService } from '@services/filter.service';

@Component({
  selector: 'app-area-list',
  templateUrl: './area-list.component.html',
  styleUrls: ['./area-list.component.scss'],
})
export class AreaListComponent extends BaseComponent implements OnInit {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;
  string_filter: string = '';
  // breadcrumbs
  links: Array<PageLink> = [
    {
      title: 'Admin Settings',
      path: '',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: 'Area Management',
      path: 'settings/area/list',
      isActive: false,
    },
    {
      title: '',
      path: '',
      isActive: false,
      isSeparator: true,
    },
    {
      title: '',
      path: '',
      isActive: true,
      attributes: 'Area List',
    },
  ];
  // table
  areaList: IAreaRoot[];
  tableColumns: TableColumn[];
  displayedColumns: string[];
  baseDatasource: BaseDatasource<IAreaRoot>;
  finishLoading$: Observable<boolean> = new BehaviorSubject(false);
  totalCount: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  privilegeCreate: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeDetail: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_FIELD_NONE;
  header: boolean = true;

  constructor(
    private pageInfoService: PageInfoService,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private utilsService: UtilitiesService,
    private baseTableService: BaseTableService<IAreaRoot>,
    private urlParamService: UrlUtilsService,
    private rolePrivilegeService: RolePrivilegeService,
    private filterService: FilterService
  ) {
    super();
  }

  @ViewChild('fileImportInput') fileImportInput: any;

  ngOnInit(): void {
    this.handlePrivilege();
    this.baseTableService.responseDatabase.subscribe((response) => (this.baseDatasource = response));
    this.queryHandler();
    this.finishLoading$ = this.baseDatasource.finishLoading;
    this.initPageInfo();
    this.setTableData();
  }

  handlePrivilege() {
    this.privilegeDetail.next(this.rolePrivilegeService.checkPrivilege('SETTINGS', 'AREA_MANAGEMENT', 'LIST_AREA', 'CTA_VIEW_DETAIL'));
    this.privilegeCreate.next(this.rolePrivilegeService.checkPrivilege('SETTINGS', 'AREA_MANAGEMENT', 'LIST_AREA', 'CTA_CREATE_AREA'));
  }

  initPageInfo() {
    this.baseDatasource.tableSubject.subscribe((val) => {
      this.areaList = val;
      this.pageInfoService.updateBreadcrumbs(this.links);
    });
  }

  setTableData() {
    this.tableColumns = [
      {
        key: 'code',
        title: 'Kode Area',
        isSortable: false,
      },
      {
        key: 'name',
        title: 'Nama Area',
        isSortable: true,
      },
      {
        key: 'province_list',
        title: 'Cakupan Provinsi',
        isSortable: false,
      },
      {
        key: 'warehouse_list',
        title: 'Gudang',
        isSortable: false,
      },
      {
        key: 'total_sub_area',
        title: 'Jumlah Sub Area',
        isSortable: true,
      },
    ];
    this.tableColumns = this.utilsService.privilegeTableColumns(this.privilegeDetail.value, this.tableColumns);
    this.displayedColumns = this.tableColumns.map((item) => item.key);
  }

  sortTable(param: Sort) {
    const sortby = this.tableColumns.find((column) => column.key === param.active);
    param.active = sortby?.key ?? '';
    this.sortDataSource(param);
  }

  sortDataSource(sortParams: Sort) {
    const keyName = sortParams.active as keyof IAreaRoot;
    if (sortParams.direction === 'asc') {
      this.areaList = this.areaList.sort((a, b) => a[keyName].toString().localeCompare(b[keyName].toString()));
    } else if (sortParams.direction === 'desc') {
      this.areaList = this.areaList.sort((a, b) => b[keyName].toString().localeCompare(a[keyName].toString()));
    }
    this.baseDatasource.tableSubject.next(this.areaList);
  }

  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  handleAction(val: any) {
    return this.router.navigate([`/settings/area/${val}`]);
  }

  renderArrayStringList(arr: [], key = '') {
    if (!arr) return;
    try {
      return this.utilsService.arrayStringJoin(arr, key, ',');
    } catch (error) {
      return;
    }
  }

  handleAdd() {
    return this.router.navigate(['/settings/area/create'], { queryParams: { selectedType: 'AREA', selectedAreaId: null } });
  }

  updateTotalCount() {
    this.baseDatasource.totalItem$.subscribe((data) => {
      if (data) {
        this.totalCount.next(data);
      }
    });
  }

  updateCount() {
    this.baseDatasource.totalItem$.subscribe((data) => {
      if (data) {
        this.pageInfoService.updateTitle(`Area List (` + data + `)`);
      } else {
        this.pageInfoService.updateTitle(`Area List (0)`);
      }
    });
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      const param = this.urlParamService.sliceQueryParams();
      if (param) {
        this.baseTableService.loadDataTable(API.AREA.GET_LIST, param);
      } else {
        this.baseTableService.loadDataTable(API.AREA.GET_LIST, '');
      }
      this.updateTotalCount();
      this.updateCount();
    });
  }

  handleSearch(event: string) {
    const snapshot = this.activeRoute.snapshot;
    const dataParam = { ...snapshot.queryParams };
    if (event) {
      delete dataParam.page;
      this.router
        .navigate([], {
          queryParams: {
            ...dataParam,
            string_filter: '' || event,
          },
        })
        .then(() => null);
    } else {
      delete dataParam.string_filter;
      this.router.navigate([], { queryParams: dataParam }).then(() => null);
      this.string_filter = '';
    }
  }
}
