.detail-card-products {
  //.left-section{
  //  width:  40%;
  //}
  .right-section{
    flex: 1;
    //width: 30%;
  }

  .paginator-group {
    margin-top: 40px;
    width: 80% !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    .swiper-button {
      background-color: white;
      border: 1px solid #F8F8F8;
      height: 24px;
      width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;
      &:hover {
        background-color: #f6f6f6;
      }

      &.button-next {
        transform: rotate(180deg);
      }
    }

    .image-container {
      height: 60px;
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px !important;

      &.active {
        border: 1px solid;
      }

      img {
        border-radius: 4px !important;
      }
    }
  }
}

.w-100px {
  width: 50px;
}

