<ng-container *ngIf="isEmptyData(); else QRListTpl">
  <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_QR_LIST_EMPTY" [text]="'Belum terdapat histori generate QR'">
    <button *ngIf="PrivilegeAddBatch" mat-raised-button color="primary" (click)="handleActionAddBatch()" class="btn btn-primary mt-5" type="button">
      <span>Tambah Batch</span>
    </button>
  </app-card-empty>
  <div class="mb-8"></div>
</ng-container>

<ng-template #QRListTpl>
  <app-card [cardBodyClasses]="'pt-2'" [header]="true">
    <ng-container cardHeader>
      <app-filter-list-qr-product
        [searchInputValue]="search"
        [filterInputActivated]="isActiveFilter"
        [finishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
        [showAddBatch]="PrivilegeAddBatch"
      />
    </ng-container>

    <ng-container *ngIf="searchNotFound(); else tableDataTpl" cardBody>
      <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_QR_LIST_EMPTY" [text]="'Batch Generate QR tidak ditemukan.'"></app-card-empty>
    </ng-container>

    <ng-template #tableDataTpl>
      <div class="table-responsive">
        <table (matSortChange)="sortTable($event)" mat-table class="table w-100 gy-5 table-row-bordered align-middle" [dataSource]="baseDatasource" matSort>
          <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
            <ng-container *ngIf="tableColumn.isSortable; else notSortable">
              <th *matHeaderCellDef [ngClass]="renderThClass(tableColumn.key)" [mat-sort-header]="tableColumn.key" class="px-3" mat-header-cell>
                {{ tableColumn.title }}
              </th>
            </ng-container>

            <ng-template #notSortable>
              <th *matHeaderCellDef [ngClass]="renderThClass(tableColumn.key)" class="px-3" mat-header-cell>
                {{ tableColumn.title }}
              </th>
            </ng-template>

            <td *matCellDef="let element" class="px-3" [ngClass]="{ 'mw-lg-300px': tableColumn.key === 'batch_name' }">
              <ng-container [ngSwitch]="tableColumn.key">
                <div *ngSwitchCase="'createdDate'">
                  <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div>{{ utilitiesService.timeStampToDate(element['create_date'], 'dd-MM-yyyy') }}</div>
                    <div>{{ utilitiesService.timeStampToDate(element['create_date'], 'HH:mm:ss') }}</div>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'request_generate_qr_status_name'">
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span class="badge badge__status {{ 'badge__status--' + element['request_generate_qr_status'] }}">
                      {{ element[tableColumn.key] }}
                    </span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'modifiedDate'">
                  <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div>{{ utilitiesService.timeStampToDate(element['update_date'], 'dd-MM-yyyy') }}</div>
                    <div>{{ utilitiesService.timeStampToDate(element['update_date'], 'HH:mm:ss') }}</div>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'actions'" class="d-flex align-items-center justify-content-center">
                  <app-table-content [type]="'icon'" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span (click)="handleActionDetail(element['id'])" [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 me-2 cursor-pointer"></span>
                  </app-table-content>

                  <ng-container *ngIf="PrivilegeDownloadBatch">
                    <app-table-content [type]="'icon'" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                      <ng-container *ngIf="isAbleToDownloadBatch(element['request_generate_qr_status']); else unableDownloadTpl">
                        <span (click)="handleActionDownload(element)" [inlineSVG]="'./assets/media/icons/ic_download.svg'" class="svg-icon svg-icon-3 ms-2 cursor-pointer"></span>
                      </ng-container>
                      <ng-template #unableDownloadTpl>
                        <span [inlineSVG]="'./assets/media/icons/ic_download.svg'" class="svg-icon svg-icon-3 ms-2" style="filter: grayscale(1); opacity: 0.5"></span>
                      </ng-template>
                    </app-table-content>
                  </ng-container>
                </div>

                <div *ngSwitchDefault>
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] ?? '-' }}</span>
                  </app-table-content>
                </div>
              </ng-container>
            </td>
          </ng-container>

          <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>

        <div class="d-flex justify-content-between py-4">
          <app-mai-material-bottom-table
            (changePage)="changePageEvent($event)"
            [baseDataTableComponent]="baseDatasource"
            [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
            class="w-100"
          ></app-mai-material-bottom-table>
        </div>
      </div>
    </ng-template>
  </app-card>
</ng-template>

<app-modal-confirm-download #modalConfirmDownload [selectedBatch]="selectedQrBatchToDownload"></app-modal-confirm-download>
