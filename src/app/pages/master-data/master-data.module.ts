import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MasterDataRoutingModule} from './master-data-routing.module';
import {ComponentsModule} from '@shared/components/components.module';
import {MatTableModule} from '@angular/material/table';
import {MatSortModule} from '@angular/material/sort';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {InlineSVGModule} from 'ng-inline-svg-2';
import {SwiperModule} from 'swiper/angular';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {NgbPopoverModule} from '@ng-bootstrap/ng-bootstrap';
import {MatTooltipModule} from '@angular/material/tooltip';
import {ProductsModule} from '../products/products.module';
import {MatInputModule} from '@angular/material/input';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatIconModule} from '@angular/material/icon';
import {MatRadioModule} from '@angular/material/radio';
import {DirectiveModule} from '@directives/directive.module';
import {ListMasterDataComponent} from "./list-master-data/list-master-data.component";
import {MatChipsModule} from "@angular/material/chips";
import {MasterDataFormSettingsComponent} from "./master-data-form-settings/master-data-form-settings.component";
import {MasterDataFormComponent} from "./master-data-form-settings/master-data-form/master-data-form.component";

@NgModule({
  declarations: [ListMasterDataComponent, MasterDataFormSettingsComponent, MasterDataFormComponent],
  imports: [
    CommonModule,
    MatTableModule,
    MatSortModule,
    MasterDataRoutingModule,
    ComponentsModule,
    MatProgressSpinnerModule,
    InlineSVGModule,
    SwiperModule,
    FormsModule,
    NgbPopoverModule,
    ReactiveFormsModule,
    MatTooltipModule,
    ProductsModule,
    MatInputModule,
    MatDatepickerModule,
    MatIconModule,
    MatRadioModule,
    DirectiveModule,
    MatChipsModule,
  ],
  exports: [],
})
export class MasterDataModule {
}
