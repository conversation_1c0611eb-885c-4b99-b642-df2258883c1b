import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RewardProductFormComponent } from './reward-product/reward-product-form/reward-product-form.component';
import { ListRewardComponent } from './reward-product/list-reward/list-reward.component';
import { ListLevelRewardComponent } from './reward-retailer/list-level-reward/list-level-reward.component';
import { DetailRewardComponent } from './reward-product/detail-reward/detail-reward.component';
import { ListRetailerRewardComponent } from './reward-retailer/list-retailer-reward/list-retailer-reward.component';
import { HistoryRewardComponent } from './reward-product/detail-reward/history-reward/history-reward.component';
import { RewardRetailerFormComponent } from './reward-retailer/reward-retailer-form/reward-retailer-form.component';
import { DetailRetailerRewardComponent } from './reward-retailer/list-retailer-reward/detail-retailer-reward/detail-retailer-reward.component';

const routes: Routes = [
  {
    path: '',
    component: ListRewardComponent,
    data: {
      enum: 'REWARD_PRODUCT',
    },
  },

  // PRODUCT
  {
    path: 'product/list',
    component: ListRewardComponent,
    data: {
      enum: 'REWARD_PRODUCT',
    },
  },
  {
    path: 'product/form',
    component: RewardProductFormComponent,
    data: {
      enum: 'REWARD_PRODUCT',
    },
  },
  {
    path: 'product/:status/:id',
    component: DetailRewardComponent,
  },
  {
    path: 'product/:status/history-reward/:id',
    component: HistoryRewardComponent,
  },

  // REWARD RETAILER
  {
    path: 'retailer-level/list',
    component: ListLevelRewardComponent,
    data: {
      enum: 'REWARD_RETAILER',
    },
  },
  {
    path: 'retailer-level/retailer/list/:id',
    component: ListRetailerRewardComponent,
  },
  {
    path: 'retailer-level/retailer/detail/:id',
    component: DetailRetailerRewardComponent,
  },
  {
    path: 'retailer-level/retailer/form',
    component: RewardRetailerFormComponent,
    data: {
      enum: 'REWARD_RETAILER',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RewardSettingRoutingModule {}
