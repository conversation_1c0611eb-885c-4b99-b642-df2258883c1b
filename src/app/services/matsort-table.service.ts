import { Injectable } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { TableColumn } from '@shared/interface/table.interface';
import { IQueryParams } from '@shared/interface/urlparam.interface';
import { ActivatedRoute, Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class MatSortTableService {
  constructor(private activeRoute: ActivatedRoute, private router: Router) {}

  sortTable(_param: Sort, _tableColumns: TableColumn[]) {
    const sortby = _tableColumns.find((column) => column.key === _param.active);
    _param.active = sortby?.key ?? '';

    return this.sortDataSource(_param);
  }
  sortDataSource(_sortParams: Sort) {
    if (_sortParams.direction) {
      const dataParam: IQueryParams = {
        sort: _sortParams.active + ',' + _sortParams.direction,
      };

      this.router
        .navigate([], {
          queryParams: {
            ...dataParam,
          },
          queryParamsHandling: 'merge',
        })
        .then(() => null);
    } else {
      let snapshot = this.activeRoute.snapshot;
      const dataParam = { ...snapshot.queryParams };
      delete dataParam.sort;

      this.router.navigate([], { queryParams: dataParam }).then(() => null);
    }
  }
}
