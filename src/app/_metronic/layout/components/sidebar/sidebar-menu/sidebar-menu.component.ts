import { Component, OnDestroy, OnInit } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { Route, Routes } from '@angular/router';

import { Routing } from 'src/app/pages/routing';
import { AuthService } from '@services/auth.service';
import { IPrivileges, IRolePrivilege } from '../../../../../pages/adminsettings/role/role.interface';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { UtilitiesService } from '@services/utilities.service';
import { routes } from '../../../../../app-routing.module';

@Component({
  selector: 'app-sidebar-menu',
  templateUrl: './sidebar-menu.component.html',
  styleUrls: ['./sidebar-menu.component.scss'],
})
export class SidebarMenuComponent implements OnInit, OnDestroy {
  routes: Routes = [];
  routesSubject: BehaviorSubject<Routes>;
  rolePrivilegeSubject = new BehaviorSubject<IRolePrivilege>({} as IRolePrivilege);

  // Registrasi Distributor
  privilegeDistributorTabAll: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeDistributorTabRegistered: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeDistributorTabActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  // Pending Distributor
  privilegePendingDistributorTabRegistration: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegePendingDistributorTabVerification: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  // Ditolak Distributor
  privilegeDitolakDistributorTabRegistration: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeDitolakDistributorTabVerification: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  private unsubscribe: Subscription[] = [];

  constructor(private authService: AuthService, private rolePrivilegeService: RolePrivilegeService, public utilities: UtilitiesService) {
    this.routesSubject = new BehaviorSubject<Routes>([]);
  }

  ngOnInit(): void {
    this.getPrivilegeByRole();
  }

  handleUrlDistributor(data: any) {
    for (const child of data.children) {
      if (child.enum === 'LIST_TEREGISTRASI') {
        this.checkTeregistrasi(data);
      }
      if (child.enum === 'LIST_MENUNGGU') {
        this.checkMenunggu(data);
      }
      if (child.enum === 'LIST_DITOLAK') {
        this.checkDitolak(data);
      }
    }
  }

  checkTeregistrasi(data: any) {
    this.privilegeDistributorTabAll.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_TEREGISTRASI', 'TAB_LIST_ALL'));
    this.privilegeDistributorTabActive.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_TEREGISTRASI', 'TAB_LIST_ACTIVE'));
    this.privilegeDistributorTabRegistered.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_TEREGISTRASI', 'TAB_LIST_REGISTERED'));

    if (this.privilegeDistributorTabActive.value) {
      data.children[0].path = 'distributor/teregistrasi/active';
    } else if (this.privilegeDistributorTabRegistered.value) {
      data.children[0].path = 'distributor/teregistrasi/registered';
    } else {
    }
  }

  checkMenunggu(data: any) {
    this.privilegePendingDistributorTabRegistration.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_MENUNGGU', 'TAB_LIST_REGISTRATION_REQUEST'));
    this.privilegePendingDistributorTabVerification.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_MENUNGGU', 'TAB_LIST_VERIFICATION_REQUEST'));

    if (this.privilegePendingDistributorTabRegistration.value) {
      data.children[1].path = 'distributor/pending/registration';
    } else if (this.privilegePendingDistributorTabVerification.value) {
      data.children[1].path = 'distributor/pending/verification';
    } else {
    }
  }

  checkDitolak(data: any) {
    this.privilegeDitolakDistributorTabRegistration.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_DITOLAK', 'TAB_LIST_REGISTRATION_REJECT'));
    this.privilegeDitolakDistributorTabVerification.next(this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'LIST_DITOLAK', 'TAB_LIST_VERIFICATION_REJECT'));

    if (this.privilegeDitolakDistributorTabRegistration.value) {
      data.children[2].path = 'distributor/reject/registration';
    } else if (this.privilegeDitolakDistributorTabVerification.value) {
      data.children[2].path = 'distributor/reject/verification';
    } else {
    }
  }

  getPrivilegeByRole() {
    let _rolePrivilege: IRolePrivilege = {
      role: this.authService.getRoleFromToken(),
      privilege: [],
    };

    const _rolePrivilegeSubs = this.rolePrivilegeService.currentRolePrivilege$.subscribe((resp) => {
      if (resp) {
        _rolePrivilege.privilege = resp as unknown as IPrivileges[];

        if (!Object.keys(_rolePrivilege.privilege).length) {
          this.rolePrivilegeSubject.next(this.rolePrivilegeService.getDataUserPrivileges());
        }

        this.rolePrivilegeSubject.next(_rolePrivilege);
      }
    });

    const _routesByRole = this.routesByRolePrivilege();
    this.routesSubject.next(_routesByRole);

    this.unsubscribe.push(_rolePrivilegeSubs);
  }

  routesByRolePrivilege() {
    let _routing: Routes;
    let _filteredRouting: Routes = [];

    const _privilegesState = this.rolePrivilegeSubject.value.privilege;

    let _initialRedirectRoute = {
      path: '',
      redirectTo: '',
      patMatch: 'full',
    };

    _routing = Routing.filter((item) => item.path !== '');
    _routing.map((routingItem: Route) => {
      if (!routingItem.data) {
        return;
      }

      const { data } = routingItem;
      _privilegesState.forEach((privilegeItem) => {
        if (data && data.enum === privilegeItem.name) {
          _filteredRouting.push(routingItem);
        }
      });
    });

    _filteredRouting = _filteredRouting.filter((routingItem) => {
      if (!routingItem.data) {
        return;
      }
      const { data } = routingItem;

      if (data.enum === 'DISTRIBUTOR') {
        this.handleUrlDistributor(data);
      }

      const _routingHasChild = !!data.children.length;

      return _privilegesState.map((privilegeItem) => {
        if (data.enum === privilegeItem.name) {
          if (_routingHasChild) {
            let _dataChild;
            _dataChild = data.children.filter((dcItem: Route | any) => {
              let { children } = dcItem;
              const _dcItemHasChild = !!children?.length;

              if (_dcItemHasChild) {
                let _dataGrandChild: any[] = [];
                children.filter((dcItemChild: any) => {
                  privilegeItem.child.forEach((picItem) => {
                    picItem.child.find((item) => {
                      if (item.detailPage === dcItemChild.enum) {
                        _dataGrandChild.push(dcItemChild);
                      }
                    });
                  });
                });

                dcItem.children = _dataGrandChild;
              }

              return privilegeItem.child.find((pcItem) => {
                if (pcItem.pageView === dcItem.enum) {
                  return dcItem;
                }
              });
            });

            data.children = _dataChild;
          }

          return routingItem;
        }
      });
    });

    // add 404 redirect
    _filteredRouting.push(_initialRedirectRoute, {
      path: '**',
      redirectTo: 'error/404',
    });

    this.routes = _filteredRouting;
    return _filteredRouting;
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
