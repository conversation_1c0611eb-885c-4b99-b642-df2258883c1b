import { ChangeDetectionStrategy, Component, OnInit, Renderer2 } from '@angular/core';
import { TranslationService } from './modules/i18n';
import { ScriptService } from '@services/script-service.service';
import { GoogleMapsLoaderService } from '@services/google-maps-loader.service';
// language list
import { locale as enLang } from './modules/i18n/vocabs/en';
import { locale as chLang } from './modules/i18n/vocabs/ch';
import { locale as esLang } from './modules/i18n/vocabs/es';
import { locale as jpLang } from './modules/i18n/vocabs/jp';
import { locale as deLang } from './modules/i18n/vocabs/de';
import { locale as frLang } from './modules/i18n/vocabs/fr';

// import { ThemeModeService } from '@metronic/partials/layout/theme-mode-switcher/theme-mode.service';

@Component({
  // tslint:disable-next-line:component-selector
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'body[root]',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit {
  zohoPath: string = 'https://desk.zoho.com/portal/api/web/inapp/854972000000508001?orgId=810751520';

  constructor(
    private translationService: TranslationService, // private modeService: ThemeModeService
    private renderer: Renderer2,
    private scriptService: ScriptService,
    private googleMapsLoader: GoogleMapsLoaderService
  ) {
    // register translations
    this.translationService.loadTranslations(enLang, chLang, esLang, jpLang, deLang, frLang);
  }

  ngOnInit() {
    // this.addScriptZoho();
    this.loadGoogleMaps();
  }

  addScriptZoho() {
    const scriptElement = this.scriptService.loadJsScript(this.renderer, this.zohoPath);
    scriptElement.onload = () => {};
    scriptElement.onerror = () => {};
  }

  loadGoogleMaps() {
    return this.googleMapsLoader.loadGoogleMaps();
  }
}
