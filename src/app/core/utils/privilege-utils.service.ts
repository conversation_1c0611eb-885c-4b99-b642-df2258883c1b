import { Injectable } from '@angular/core';
import { TableColumn } from '@shared/interface/table.interface';

@Injectable({ providedIn: 'root' })
export class PrivilegeUtilsService {
  privilegeTableColumns(privilege: boolean, tableColumns: TableColumn[]): TableColumn[] {
    const hasActions = tableColumns.some((col) => col.key === 'actions');

    if (privilege && !hasActions) {
      return [...tableColumns, { key: 'actions', title: 'Actions' }];
    }

    if (!privilege && hasActions) {
      return tableColumns.filter(col => col.key !== 'actions');
    }

    return tableColumns;
  }

  privilegeConfidentialDocuments(privileges: { name: string }[], confidentials: any[]): { label: string; value: string }[] {
    const result: { label: string; value: string }[] = [];

    if (!privileges.length || !confidentials.length) return result;

    confidentials.forEach((confidential) => {
      if (!confidential?.name) {
        result.push(confidential);
      } else {
        const matched = privileges.find((p) => p.name === confidential.name);
        if (matched) result.push(confidential);
      }
    });

    return result;
  }

  getStatusDetailPrivilege(status: string | null | undefined, DetailEnum: any): string | null {
    if (!status) return null;
    return DetailEnum[status] ?? null;
  }
}
