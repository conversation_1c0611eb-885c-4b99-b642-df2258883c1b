import { Injectable } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Injectable({ providedIn: 'root' })
export class DomUtilsService {
  constructor() {}

  reloadPage(): void {
    window.location.reload();
  }

  copyToClipboard(data: string): Promise<void> {
    return navigator.clipboard.writeText(data);
  }

  setDefaultImageProduct(e: Event): HTMLImageElement {
    const target = e.target as HTMLImageElement;
    target.src = STRING_CONSTANTS.ILLUSTRATIONS.DEFAULT_IMAGE;
    return target;
  }

  otherToggleSelection(selectedList: string[], e: Event): boolean {
    const value = (e.target as HTMLInputElement).value;
    const index = selectedList.indexOf(value);

    if (index > -1) {
      selectedList.splice(index, 1);
      return value !== 'OTHER';
    } else {
      selectedList.push(value);
      return value === 'OTHER';
    }
  }

  getBrowserName(overrideUA?: string): string {
    const agent = (overrideUA ?? window.navigator.userAgent).toLowerCase();

    if (agent.includes('edg')) return 'edge';
    if (agent.includes('opr')) return 'opera';
    if (agent.includes('chrome') && !agent.includes('edg') && !agent.includes('opr')) return 'chrome';
    if (agent.includes('trident')) return 'ie';
    if (agent.includes('firefox')) return 'firefox';
    if (agent.includes('safari') && !agent.includes('chrome')) return 'safari';
    return 'other';
  }

  checkInHasNewValue(hasValue: boolean): string {
    return hasValue ? 'border border-primary' : '';
  }
}
