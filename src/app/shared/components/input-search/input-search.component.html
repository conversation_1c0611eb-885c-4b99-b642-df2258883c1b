<div class="d-flex align-items-center position-relative my-1" style="min-width: 320px">
  <div class="input-group" *ngIf="isFinishLoadingSubject | async; else loaderInputSearch">
    <input type="text" class="form-control" placeholder="{{ placeholder }}" [(ngModel)]="value" (keydown.enter)="handleSubmit(value)" />
    <button (click)="handleSubmit(value)" class="input-group-text">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_SEARCH" class="svg-icon svg-icon-2"></span>
    </button>
    <button *ngIf="value" (click)="handleReset()" class="input-group-text bg-none border-none">
      <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_SEARCH_RESET" class="svg-icon svg-icon-2"></span>
    </button>
  </div>
</div>

<ng-template #loaderInputSearch>
  <ngx-skeleton-loader count="1" [theme]="{ 'border-radius': '5', height: '40px', width: '300px' }"></ngx-skeleton-loader>
</ng-template>
