<div class="button-group d-flex align-items-center mt-5">
  <ng-container [ngTemplateOutlet]="isVerified || isRequestRevision ? resetAct : otherAct"></ng-container>

  <ng-template #resetAct>
    <ng-container *ngIf="showBtnVerify || showBtnRequestRevision">
      <button type="button" class="btn btn-transparent p-0 d-flex align-items-center" (click)="actionVerification('RESET')">
        <span class="svg-icon svg-icon-24 me-4" [inlineSVG]="STRING_CONSTANTS.ICON.IC_RESET"></span>
        <span class="text-secondary-blue fw-bolder">Reset Verifikasi</span>
      </button>
    </ng-container>
  </ng-template>

  <ng-template #otherAct>
    <ng-container *ngIf="showBtnVerify">
      <button type="button" class="btn btn-transparent p-0 d-flex align-items-center" (click)="actionVerification('VERIFY')">
        <span class="svg-icon svg-icon-24 me-4" [inlineSVG]="STRING_CONSTANTS.ICON.IC_OUTLINE_BULLET_TICK"></span>
        <span class="text-primary fw-bolder">Verifikasi</span>
      </button>
    </ng-container>

    <ng-container *ngIf="showBtnVerify && showBtnRequestRevision">
      <span class="button-group__separator mx-5"></span>
    </ng-container>

    <ng-container *ngIf="showBtnRequestRevision">
      <button type="button" class="btn btn-transparent p-0 d-flex align-items-center" (click)="actionVerification('REVISION')">
        <span class="svg-icon svg-icon-24 me-4" [inlineSVG]="STRING_CONSTANTS.ICON.IC_OUTLINE_EDIT_DANGER"></span>
        <span class="text-danger fw-bolder">Request Perbaikan</span>
      </button>
    </ng-container>
  </ng-template>
</div>
