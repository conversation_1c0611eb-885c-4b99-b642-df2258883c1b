import { Component, EventEmitter, Input, Output } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { EnumVerificationStatus } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.enum';
import { ProgramMarketingLegacyService } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.service';

@Component({
  selector: 'app-button-group-verify',
  templateUrl: './button-group-verify.component.html',
  styleUrls: ['./button-group-verify.component.scss'],
})
export class ButtonGroupVerifyComponent {
  @Input() sectionStatusVerification!: EnumVerificationStatus | null;
  @Input() showBtnVerify: boolean = true;
  @Input() showBtnRequestRevision: boolean = true;

  @Output() ctaVerify = new EventEmitter();
  @Output() ctaResetVerification = new EventEmitter();
  @Output() ctaRequestRevision = new EventEmitter();

  @Output() ctaVerification = new EventEmitter<EnumVerificationStatus | null>();

  actionVerify = () => this.ctaVerify.emit(true);

  actionResetVerification = () => this.ctaResetVerification.emit(true);

  actionRequestRevision = () => this.ctaRequestRevision.emit(true);

  actionVerification(act: 'VERIFY' | 'RESET' | 'REVISION') {
    const _actionEnum = act === 'VERIFY' ? EnumVerificationStatus.VERIFIED : act === 'REVISION' ? EnumVerificationStatus.REQUEST_REVISION : null;
    return this.ctaVerification.emit(_actionEnum);
  }

  get isVerified() {
    return this.sectionStatusVerification === EnumVerificationStatus.VERIFIED;
  }

  get isRequestRevision() {
    return this.sectionStatusVerification === EnumVerificationStatus.REQUEST_REVISION;
  }

  constructor(public promagService: ProgramMarketingLegacyService) {}

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
