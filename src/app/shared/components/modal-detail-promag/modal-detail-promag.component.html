<app-modal #modalDetail [modalConfig]="modalDetailConfig" [modalOptions]="{ size: 'lg' }">
  <ng-container [ngTemplateOutlet]="informationContentTpl"></ng-container>
  <ng-container *ngIf="InformationProgram" [ngSwitch]="InformationProgram.program_type_enum">
    <ng-container *ngSwitchCase="EnumProgramMarketingType.ONE_SHOOT"
                  [ngTemplateOutlet]="oneShootTpl"></ng-container>
    <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT"
                  [ngTemplateOutlet]="discountProductTpl"></ng-container>
    <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE"
                  [ngTemplateOutlet]="discountPurchaseTpl"></ng-container>
    <ng-container *ngSwitchDefault [ngTemplateOutlet]="oneShootTpl"></ng-container>
  </ng-container>

  <ng-template #oneShootTpl>
    <ng-container [ngTemplateOutlet]="programTermContentTpl"></ng-container>
    <ng-container [ngTemplateOutlet]="orderTermContentTpl"></ng-container>
    <ng-container [ngTemplateOutlet]="discountTermContentOneShootTpl"></ng-container>
    <ng-container [ngTemplateOutlet]="rewardContentTpl"></ng-container>
  </ng-template>

  <ng-template #discountProductTpl>
    <ng-container [ngTemplateOutlet]="programTermContentTpl"></ng-container>
    <ng-container [ngTemplateOutlet]="discountTermContentProductTpl"></ng-container>
  </ng-template>

  <ng-template #discountPurchaseTpl>
    <ng-template [ngTemplateOutlet]="programTermContentTpl"></ng-template>
    <ng-container [ngTemplateOutlet]="orderTermContentTpl"></ng-container>
    <ng-container [ngTemplateOutlet]="discountTermContentPurchaseTpl"></ng-container>
  </ng-template>
</app-modal>

<ng-template #informationContentTpl>
  <div *ngIf="InformationProgram" class="mb-5">
    <h5 class="fw-bold mb-4">Informasi Program Marketing</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Tipe Program</div>
      <div>: {{ InformationProgram.program_type_string }}</div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Periode</div>
      <div>
        : {{ InformationProgram.period_start ? utils.formatEpochToDate(InformationProgram.period_start, 'dd-MM-yyyy') : '-' }}
        s/d
        {{ InformationProgram.period_end ? utils.formatEpochToDate(InformationProgram.period_end, 'dd-MM-yyyy') : '-' }}
      </div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Catatan Management</div>
      <div>: {{ InformationProgram.management_note ? InformationProgram.management_note : '-' }}</div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Nomor Surat Pengajuan</div>
      <div>: {{ InformationProgram.reference_number }}</div>
    </div>
  </div>
</ng-template>

<ng-template #programTermContentTpl>
  <div *ngIf="ProgramTerm" class="mb-5">
    <h5 class="fw-bold mb-4">Ketentuan Program</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Cakupan Program</div>
      <div>: {{ ProgramTerm.scope_string }}</div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Kuota</div>
<!--      todo: need enhance render quota - use updated interface (IProgramMarketingDetail__ProgramTerm)-->
<!--      <ng-container *ngFor="let quota of ProgramTerm.quota; let f = first">-->
<!--        <div *ngIf="f; else defaultQuota">: {{ quota }}</div>-->
<!--        <ng-template #defaultQuota>-->
<!--          <div class="ms-2">{{ quota }}</div>-->
<!--        </ng-template>-->
<!--      </ng-container>-->
    </div>
  </div>
</ng-template>

<ng-template #orderTermContentTpl>
  <div *ngIf="OrderTermProgram" class="mb-5">
    <h5 class="fw-bold mb-4">Ketentuan Pembelian</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Aturan Pembelian 1 PO</div>
      <div>: {{ utils.mapKeyToString(EnumProgramMarketingPOTypeString, OrderTermProgram.purchase_order_type_enum) }}
      </div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Produk Dibeli</div>
      <div class="d-flex">
        <span class="me-1">:</span>
        <div [innerHTML]="renderPOProduct(OrderTermProgram.purchase_order_type_enum)"></div>
      </div>
    </div>
    <ng-container *ngIf="OrderTermProgram.purchase_order_type_enum === EnumProgramMarketingPOType.ACCUMULATION">
      <div class="d-flex my-2">
        <div class="text-gray-700 min-w-200">Produk Dibeli</div>
        <div class="d-flex">
          <span class="me-1">:</span>
          {{ renderMinimumBuy(OrderTermProgram) }}
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #discountTermContentOneShootTpl>
  <div *ngIf="DiscountTerm" class="mb-5">
    <h5 class="fw-bold mb-4">Ketentuan Diskon</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Cakupan Diskon</div>
      <div>: {{ DiscountTerm.discount_scope }}</div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Diskon Program</div>
      <div class="d-flex">
        <span class="me-1">:</span>
        <span [innerHTML]="discountPurchaseProducts(DiscountTerm.discount_purchases)"
              class="d-flex flex-wrap flex-column"></span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #discountTermContentProductTpl>
  <div *ngIf="DiscountTerm" class="mb-5">
    <h5 class="fw-bold mb-4">Ketentuan Diskon</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Tipe Diskon</div>
      <div>
        : {{ utils.mapKeyToString(EnumProgramMarketingDiscountCategoryString, DiscountTerm.discount_category) ?? '-' }}
      </div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Jenis Diskon</div>
      <span>: {{ utils.mapKeyToString(EnumProgramMarketingDiscountTypeString, DiscountTerm.discount_type_enum) ?? '-' }}</span>
    </div>
    <div class="table-responsive">
      <table [dataSource]="tableDiscountProducts" class="table w-100 gy-5 table-row-bordered align-middle" mat-table>
        <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
          <th *matHeaderCellDef class="px-3" mat-header-cell>
            {{ tableColumn.title }}
          </th>

          <td *matCellDef="let element" class="px-3">
            <ng-container [ngSwitch]="tableColumn.key">
              <div *ngSwitchDefault>
                <span>{{ element[tableColumn.key] ?? '-' }}</span>
              </div>
            </ng-container>
          </td>
        </ng-container>
        <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
  </div>
</ng-template>

<ng-template #discountTermContentPurchaseTpl>
  <div *ngIf="DiscountTerm" class="mb-5">
    <h5 class="fw-bold mb-4">Ketentuan Diskon</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Tipe Diskon</div>
      <div>
        : {{ utils.mapKeyToString(EnumProgramMarketingDiscountCategoryString, DiscountTerm.discount_category) ?? '-' }}
      </div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Diskon</div>
      <div class="d-flex">
        <span class="me-1">:</span>
        <span [innerHTML]="discountPurchaseProducts(DiscountTerm.discount_purchases)"
              class="d-flex flex-wrap flex-column"></span>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #rewardContentTpl>
  <div *ngIf="RewardProgram" class="mb-5">
    <h5 class="fw-bold mb-4">Ketentuan Hadiah</h5>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Aturan Hadiah</div>
      <div>: {{ renderIstMultiplicationType(RewardProgram.multiplication_type) }}</div>
    </div>
    <div class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Produk Hadiah</div>
      <div>:
        <span>
          <ng-container
            [ngTemplateOutlet]="RewardProgram.reward_type_enum === EnumProgramMarketingRewardType.MAI_PRODUCT ? maiProductTpl : nonMaiProductTpl">
          </ng-container>
        </span>
        <ng-template #maiProductTpl>
          {{ RewardProgram.mai_product?.name }} ({{ RewardProgram.mai_product?.qty }} <span
          class="text-capitalize">{{ RewardProgram.mai_product?.unit }})</span>
        </ng-template>
        <ng-template #nonMaiProductTpl>{{ RewardProgram.non_mai_product?.product_other_reward }}</ng-template>
      </div>
    </div>
    <div *ngIf="RewardProgram.reward_type_enum === EnumProgramMarketingRewardType.NON_MAI_PRODUCT" class="d-flex my-2">
      <div class="text-gray-700 min-w-200">Maksimal Budget</div>
      <div>: {{ utils.toRupiah(RewardProgram.non_mai_product?.maximum_budget_other_reward ?? 0) }}</div>
    </div>
  </div>
</ng-template>
