import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl, FormControlName, FormGroup, FormGroupDirective } from '@angular/forms';
import { BehaviorSubject, Observable, startWith } from 'rxjs';
import { InputSelectMaterialInterface } from '@shared/components/form/input-select-material/input-select-material.interface';
import { map } from 'rxjs/operators';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UtilitiesService } from '@services/utilities.service';
import { ControlMessage } from '@config/enum/control-message.enum';
import { MatAutocompleteSelectedEvent, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { MatSelectChange } from '@angular/material/select';
import { OverlayContainer } from '@angular/cdk/overlay';

export interface User {
  name: string;
}

@Component({
  selector: 'app-input-select-material',
  templateUrl: './input-select-material.component.html',
  styleUrls: ['./input-select-material.component.scss'],
})
export class InputSelectMaterialComponent implements OnInit, OnChanges {
  @Input() label: string;
  @Input() readOnly: boolean = false;
  @Input() required = false;
  @Input() placeholder: string;
  @Input() class?: string;
  @Input() behaviorSubjectOptions: string;
  @Input() customNameWithId = false;
  @Output() handleChangeData = new EventEmitter();
  @Input() optionsData = new BehaviorSubject<InputSelectMaterialInterface[]>([]);

  @Input() useFilterList = true; // with input to filter list dropdown
  @Input() useLabel = true;

  options: InputSelectMaterialInterface[] = [];
  optionsSelected: Observable<InputSelectMaterialInterface[]>;

  myControl = new FormControl<string | InputSelectMaterialInterface>('');

  valueFormGroup?: FormGroup;
  valueFormControl?: FormControl;

  ICONS = STRING_CONSTANTS.ICON;
  @ViewChild(MatAutocompleteTrigger) autocomplete: MatAutocompleteTrigger;

  constructor(
    private overlayContainer: OverlayContainer,
    private formGroupDirective: FormGroupDirective,
    private formControlNameDirective: FormControlName,
    public utilitiesService: UtilitiesService
  ) {
    this.overlayContainer.getContainerElement().classList.add('custom-cdk-overlay');
  }

  get isNotValidAndDirty() {
    return !this.formControlNameDirective.valid && this.formControlNameDirective.dirty;
  }

  get hasErrors() {
    return this.formControlNameDirective.errors;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.readOnly) {
      if (this.readOnly) this.valueFormControl?.disable();
      else this.valueFormControl?.enable();
    }
  }

  ngOnInit(): void {
    this.valueFormGroup = this.formGroupDirective.form;
    this.valueFormControl = this.formGroupDirective.getControl(this.formControlNameDirective);

    const _valueFormControl = this.valueFormControl.value;
    this.optionsData.subscribe((value) => {
      // this.optionsSelected=this.optionsData.asObservable();
      this.options = value;
      //
      if (_valueFormControl) {
        const _selectedOption = this.options?.find((item) => item.value === (typeof _valueFormControl === 'string' ? _valueFormControl : _valueFormControl.value));
        if (_selectedOption) {
          this.myControl?.setValue(_selectedOption);
          this.valueFormControl?.setValue(_selectedOption);
        } else {
          this.myControl.setValue('');
          this.valueFormControl?.setValue('');
        }
      } else {
        // need to set this to show list dropdown panel
        // on input text click
        this.myControl.setValue('');
        // Todo: case conflict in user form
      }
    });

    this.optionsSelected = this.myControl.valueChanges.pipe(
      startWith(''),
      map((value) => {
        const name = typeof value === 'string' ? value : value?.label;

        if (typeof value !== 'string') {
          this.valueFormControl?.setValue(value);
        }

        let labelValue = name?.split(',')[1] as string;
        if (!labelValue) {
          return this.filterOptionsSelected(name || ('' as string));
        }

        return this.filterOptionsSelected(labelValue || '');
      })
    );

    if (this.readOnly) this.valueFormControl?.disable();
    else this.valueFormControl?.enable();
  }

  filterOptionsSelected(name: string): InputSelectMaterialInterface[] {
    const filterValue = this.normalizeValue(name);
    let selectedOption = this.valueFormControl?.value;
    return this.options.filter((option) => {
      if (!selectedOption) {
        return this.normalizeValue(option.label).includes(filterValue);
      }
      return this.normalizeValue(option.label).includes(filterValue);
    });
  }

  normalizeValue(value: string) {
    if (!value) return '';
    return value.toLowerCase().replace(/\s/g, '');
  }

  get controlName() {
    return this.formControlNameDirective.name;
  }

  get enabled() {
    return this.valueFormControl?.enabled;
  }

  get isInvalid() {
    return this.valueFormControl?.invalid;
  }

  displayFn(data: InputSelectMaterialInterface): string {
    if (!data) {
      return '';
    }

    if (data && data.code) {
      return data.code + ', ' + data.label;
    }

    return data.label;
  }

  displayErrorMessage(name?: any) {
    let _message: string = ' harus diisi';
    if (name) {
      _message = this.utilitiesService.mapKeyToString(ControlMessage, name as string) + _message;
    }

    return _message;
  }

  handleChangeValue = (e: MatAutocompleteSelectedEvent) => {
    this.handleChangeData.emit(e.option.value);
  };

  handleChangeSelect(e: MatSelectChange) {
    this.handleChangeData.emit(e);
  }

  onFocus() {
    if (this.readOnly) return;
    this.autocomplete.openPanel();
  }
}
