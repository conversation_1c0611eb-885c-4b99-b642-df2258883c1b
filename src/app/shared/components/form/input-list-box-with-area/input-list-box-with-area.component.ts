import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { IChips, IChipsConfig, IChipsWithArea } from '@shared/components/v1/chips/chips.interface';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { BehaviorSubject, debounceTime, Observable } from 'rxjs';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { FormControl, FormControlName, FormGroup, FormGroupDirective } from '@angular/forms';
import { BaseResponse } from '@shared/base/base-response';
import { BaseService } from '@services/base-service.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BaseComponent } from '@shared/base/base.component';

@Component({
  selector: 'app-input-list-box-with-area',
  templateUrl: './input-list-box-with-area.component.html',
  styleUrls: ['./input-list-box-with-area.component.scss'],
})
export class InputListBoxWithAreaComponent extends BaseComponent implements OnInit, OnChanges {
  @ViewChild('modalForm') private modalFormComponent: ModalComponent;

  @Input() required: boolean = false;
  @Input() label: string;
  @Input() chipsListData: IChipsWithArea[] = [];
  @Input() UrlEndpoint: string;

  @Input() showChipsModal: boolean = true;
  @Input() disabled: boolean = false;

  @Input() btnStyleOutline: boolean = false;
  @Input() btnStyleDisabled: boolean = false;

  @Input() columnClass = { label: 'col-lg-4', content: 'col-lg-8' };
  @Input() searchKeyword = 'keyword';

  @Output() selectionOutput = new EventEmitter();

  modalFormConfig: ModalConfig;
  searchControl: FormControl;
  listBox_listItems = new BehaviorSubject<IChipsWithArea[]>([]);
  listBox_selectedItems = new BehaviorSubject<IChipsWithArea[]>([]);
  STRING_CONSTANTS = STRING_CONSTANTS;
  isLoading = new BehaviorSubject<boolean>(false);

  listBox_listItems$: Observable<BaseResponse<any[]>>;

  valueFormGroup: FormGroup;
  valueFormControl?: FormControl;
  chipsConfig: IChipsConfig = {
    disabled: this.disabled,
    labelTextStyle: 'capitalize',
    showLimit: true,
    limit: 5,
  };

  constructor(private baseService: BaseService, private formGroupDirective: FormGroupDirective, private formControlNameDirective: FormControlName, private cdr: ChangeDetectorRef) {
    super();
    this.searchControl = new FormControl('');
  }

  ngOnInit(): void {
    this.initModalForm();
    this.initListBox();
    this.changeKeyword();

    this.valueFormGroup = this.formGroupDirective.form;
    this.valueFormControl = this.formGroupDirective.getControl(this.formControlNameDirective);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.listBoxItems && !changes.listBoxItems.firstChange) {
      this.listBox_listItems = changes.listBoxItems.currentValue;
    }

    const newChips = changes.chipsListData?.currentValue ?? [];
    if (changes.chipsListData && !changes.chipsListData.firstChange && newChips.length) {
      this.chipsListData = newChips;
      this.setSelectedChipsList();
      this.mapSelectedItems(this.listBox_listItems.value);
    }

    if (changes.chipsListData && changes.chipsListData.firstChange && newChips.length) {
      this.listBox_selectedItems.next(newChips);
    }
  }

  initModalForm() {
    this.modalFormConfig = {
      modalTitle: 'Pilih ' + this.label,
    };
  }

  initListBox() {
    this.btnStyleDisabled = this.disabled;
    this.isLoading.next(true);
    return this.getListBoxData();
  }

  getListBoxData(keyword?: string) {
    const _keyword = keyword ? `?${this.searchKeyword}=` + keyword : '';
    this.listBox_listItems$ = this.baseService.getData(this.UrlEndpoint + _keyword);
    this.getData();
  }

  getData() {
    this.listBox_listItems$.pipe(this.takeUntilDestroy()).subscribe((list) => {
      if (!list?.data) return;

      this.setSelectedChipsList();

      const existingIds = new Set(this.listBox_listItems.value.map((item) => item.id));

      const filteredData = list.data
        .filter((item) => item.name) // exclude null or empty name
        .filter((item) => !existingIds.has(item.id)) // exclude existing items
        .map((item) => ({ ...item, selected: false })); // set selected to false

      const updatedList = [...this.listBox_listItems.value, ...filteredData];

      this.listBox_listItems.next(updatedList);
      this.mapSelectedItems(updatedList);
      this.cdr.detectChanges();
    });
  }

  mapSelectedItems(listItems: IChipsWithArea[]) {
    if (!(this.chipsListData ?? []).length) return;

    const selectedChips = [...this.listBox_selectedItems.value, ...this.chipsListData].filter((chip) => chip.selected);

    const selectedIds = new Set(selectedChips.map((chip) => chip.id));

    const updatedList = listItems
      .map((item) => {
        const isSelected = selectedIds.has(item.id);
        item.selected = isSelected;
        return { ...item, selected: isSelected };
      })
      .filter((item) => item.selected);

    this.listBox_selectedItems.next(updatedList);
    this.selectionOutput.emit(this.listBox_selectedItems);
  }

  handleChangeSelection() {
    this.setSelectedItems();
  }

  setSelectedItems() {
    this.listBox_selectedItems.next(this.listBox_listItems.value.filter((val: any) => val.selected));
  }

  handleDataOutput(items: IChips[]) {
    this.listBox_selectedItems.next(items as IChipsWithArea[]);
    this.selectionOutput.emit(this.listBox_selectedItems);

    this.setFormControlValue();
    this.handleRemovedChips();
    // if (this.useIndeterminateCheckbox) return this.getCheckedAll();
  }

  handleAddChipsList() {
    this.selectionOutput.emit(this.listBox_selectedItems);
    this.toggleShowSelectedList();

    this.setFormControlValue();
    return this.modalFormComponent.close();
  }

  setFormControlValue() {
    const data = this.listBox_selectedItems.value;
    const _arrId: string[] = [];
    data.forEach((item) => _arrId.push(item.id));
    this.valueFormControl?.setValue(_arrId);
  }

  toggleShowSelectedList() {
    this.chipsListData = this.listBox_selectedItems.value;
  }

  handleOpenModalForm = () => {
    this.modalFormComponent.open().then();
  };

  setSelectedChipsList() {
    this.listBox_selectedItems.value.map((chips) => (chips.selected = true));
    this.isLoading.next(false);
  }

  handleRemovedChips() {
    const removedChipsIds = new Set(this.chipsListData.filter((chip) => !chip.selected).map((chip) => chip.id));

    const updatedItems = this.listBox_listItems.value.map((item) => ({
      ...item,
      selected: removedChipsIds.has(item.id) ? false : item.selected,
    }));

    this.listBox_listItems.next(updatedItems);
  }

  changeKeyword() {
    this.searchControl.valueChanges.pipe(debounceTime(300)).subscribe((value: string) => {
      if (value.length < 3) {
        return;
      }

      const keyword = value.toLowerCase();
      const found = this.listBox_listItems.value.some(
        (item) => (item.name && item.name.toLowerCase().includes(keyword)) || (item.area && item.area.toLowerCase().includes(keyword))
      );

      if (found) {
        // Tidak perlu fetch, cukup filter di template (sudah otomatis dengan pipe filter)
        this.isLoading.next(false);
      } else {
        // Tidak ditemukan, fetch ke server
        this.isLoading.next(true);
        this.getListBoxData(value);
      }
    });
  }
}
