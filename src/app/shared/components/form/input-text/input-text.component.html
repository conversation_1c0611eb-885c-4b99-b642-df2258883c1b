<div [formGroup]="valueFormGroup" class="d-flex flex-wrap align-items-{{ textArea ? 'start' : 'center' }} mb-6">
  <label *ngIf="label" [ngClass]="{ 'col-lg-4': !columnStacked, required: required }" class="col-12 col-form-label" for="{{ controlName }}">
    {{ label }}
  </label>

  <ng-container *ngIf="extraContent">
    <ng-content></ng-content>
  </ng-container>

  <ng-container *ngIf="textArea; else notTextArea">
    <div [ngClass]="{ 'col-lg-8': !columnStacked }" class="col-12">
      <textarea
        [ngClass]="{ 'is-invalid': isNotValidAndDirty }"
        class="form-control form-control-solid min-h-100px"
        cols="10"
        formControlName="{{ controlName }}"
        placeholder="{{ placeholder }}"
        rows="3"
      ></textarea>
      <div *ngIf="isNotValidAndDirty && hasErrors" class="invalid-feedback px-2">
        <div *ngIf="hasErrors.required">{{ displayErrorMessage(controlName) }}</div>
      </div>
    </div>
  </ng-container>

  <ng-template #notTextArea>
    <div [class]="'col-12 ' + class" [ngClass]="{ 'col-lg-8': !columnStacked }">
      <input
        [ngClass]="{ 'is-invalid': isNotValidAndDirty }"
        [type]="type ?? 'text'"
        [min]="min"
        [max]="max"
        class="form-control form-control-solid {{ inputClass }}"
        formControlName="{{ controlName }}"
        pattern="{{ pattern }}"
        placeholder="{{ placeholder }}"
      />

      <div *ngIf="isNotValidAndDirty && hasErrors" class="invalid-feedback px-2">
        <div *ngIf="hasErrors.required">{{ displayErrorMessage(controlName, 'required') }}</div>
        <div *ngIf="hasErrors.minlength">{{ controlName }} must be at least {{ min }} characters</div>
        <div *ngIf="hasErrors.maxlength">{{ displayErrorMessage(controlName, 'max-length') }}</div>
        <div *ngIf="hasErrors.pattern">{{ displayErrorMessage(controlName, 'pattern') }}</div>
        <div *ngIf="hasErrors.email">{{ controlName }} is invalid</div>
        <div *ngIf="hasErrors.emailUnique">{{ displayErrorMessage(controlName, 'email-unique') }}</div>
      </div>
    </div>
  </ng-template>
</div>
