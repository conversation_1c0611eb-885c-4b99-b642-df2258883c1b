import { Component } from '@angular/core';
import { AsyncPipe, NgIf } from '@angular/common';
import { ComponentsModule } from '@shared/components/components.module';

@Component({
  selector: 'app-card-section-loader',
  standalone: true,
  imports: [AsyncPipe, ComponentsModule, NgIf],
  template: `
    <app-card [cardClasses]="'mb-8 animation animation-fade-in'">
      <ng-container cardBody><app-section-loader /></ng-container>
    </app-card>
  `,
})
export class CardSectionLoaderComponent {}
