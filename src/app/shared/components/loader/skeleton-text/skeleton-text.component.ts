import { Component, Input, OnInit } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-skeleton-text',
  templateUrl: './skeleton-text.component.html',
  styleUrls: ['./skeleton-text.component.scss'],
})
export class SkeletonTextComponent extends BaseComponent implements OnInit {
  @Input() count: number = 1;
  @Input() type: string = 'text';
  @Input() width: number = 100;
  @Input() height: number = 20;
  @Input() data: string;
  @Input() fluidSize = false;
  @Input() isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  constructor() {
    super();
  }

  typeWithNumbers = ['icon-and-text', 'text-and-text', 'text-and-image'];
  numbers: number[];

  ngOnInit(): void {
    if (this.typeWithNumbers.includes(this.type)) {
      this.numbers = Array(this.count).map((x, i) => i);
    }
  }

  renderThemeWidth() {
    return this.fluidSize ? `calc(100% - ${this.width}px)` : `${this.width}px`;
  }
}
