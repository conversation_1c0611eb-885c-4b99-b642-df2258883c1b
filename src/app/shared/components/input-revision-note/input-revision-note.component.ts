import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-input-revision-note',
  templateUrl: './input-revision-note.component.html',
  styleUrls: ['./input-revision-note.component.scss'],
})
export class InputRevisionNoteComponent implements OnInit {
  @Input() note?: string;
  @Output() noteChanged = new EventEmitter<string>();

  form!: FormGroup;

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      note: new FormControl(),
    });
  }

  get noteControl() {
    return <FormControl>this.form.get('note');
  }

  ngOnInit() {
    this.noteChanges();
  }

  noteChanges() {
    this.noteControl.valueChanges.subscribe((val) => {
      this.noteChanged.emit(val);
    });
  }
}
