<!--<input (change)="fileChangeEvent($event)" type="file"/>-->
<app-modal #modal [modalClass]="'modal-cropper'" [modalConfig]="modalConfig" [modalOptions]="{ size: 'lg' }">
  <div class="bg-black">
    <image-cropper
      (cropperReady)="cropperReady()"
      (imageCropped)="imageCropped($event)"
      (imageLoaded)="imageLoaded($event)"
      (loadImageFailed)="loadImageFailed()"
      [aspectRatio]="4 / 3"
      [canvasRotation]="canvasRotation"
      [imageURL]="imageUrl"
      [imageChangedEvent]="imageChangedEvent"
      [maintainAspectRatio]="true"
      [transform]="transform"
      class="p-0 mh-100"
      format="png"
    ></image-cropper>

    <div class="my-5 d-flex justify-content-center gap-3 align-items-center">
      <button (click)="zoomOut()" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_ZOOM_OUT_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
      <button (click)="zoomIn()" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_ZOOM_IN_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
      <button (click)="rotateLeft()" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_ROTATE_LEFT_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
      <button (click)="rotateRight()" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_ROTATE_RIGHT_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
      <button (click)="zoomIn()" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_ZOOM_IN_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
      <button (click)="flipHorizontal()" [class.enabled]="transform.flipH" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_MIRROR_HORIZONTAL_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
      <button (click)="flipVertical()" [class.enabled]="transform.flipV" class="btn-crop"><span
        [inlineSVG]="STRING_CONSTANTS.ICON.IC_MIRROR_VERTICAL_BLACK" class="svg-icon svg-icon-24"></span>
      </button>
    </div>
  </div>
</app-modal>

<form [formGroup]="uploadForm" class="d-flex flex-wrap upload-product-group" name="form__{{ idx }}">
  <ng-container formArrayName="fileImages">
    <ng-container *ngIf="fileImages && fileImages.length">
      <div *ngFor="let img of fileImages.controls; index as i">
        <div class="dropzone position-relative col-lg-4 me-3 mb-3">
          <div class="d-flex justify-content-between">
            <div *ngIf="i === 0" class="img-close-icon position-absolute top-0 start-0 banner-first-image">
              <span class="fw-bold">Gambar Utama</span>
            </div>
            <div class="">
            <span (click)="onDeleteFileImage(i,  img.value )"
                  [inlineSVG]="STRING_CONSTANTS.ICON.IC_BULLET_CLOSE"
                  class="img-close-icon position-absolute top-0 end-0 p-3"></span>
            </div>
          </div>
          <img [alt]="'preview__'+i" [formGroupName]="i" [src]="img.value.url" class="img-fluid h-100"
               style="max-height: 100%;"/>
        </div>
        <div class="d-flex justify-content-center align-items-center me-3">
          <a (click)="onEditFileImage(i)" class="text-info text-decoration-underline cursor-pointer">Edit Foto</a>
          <div class="mx-4">|</div>
          <a (click)="onReplaceFileImage(i)" class="text-info text-decoration-underline cursor-pointer">Ganti Foto</a>
        </div>
      </div>
    </ng-container>

    <ng-container *ngIf="fileImages.length < maxLimit">
      <div
        (filesChangeEmitter)="handleDropFiles($event)" [ngStyle]="{height:'200px', width : '200px'}"
        appDnd
        class="dropzone position-relative"
        style="{{ isLoading.value ? 'pointer-events: none;' : '' }}"
      >
        <ng-container *ngTemplateOutlet="loadingUpload"></ng-container>
        <label
          class="d-flex cursor-pointer align-items-center justify-content-center flex-column"
          for="fileImages__{{ idx }}"
          style="{{ isLoading.value ? 'opacity: 0.25' : '' }}"
        >
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_PLUS_DISABLED" class="svg-icon"></span>
          <span class="mt-4 text-gray-400">Add image here</span>
        </label>

        <input
          (change)="onFileSelected($event)"
          accept="image/*"
          class="d-none" id="fileImages__{{ idx }}"
          multiple
          name="fileImages__{{ idx }}"
          type="file"
        />
      </div>
    </ng-container>
  </ng-container>
</form>

<ng-template #loadingUpload>
  <span
    *ngIf="isLoading.value"
    class="indicator-progress d-flex align-items-center justify-content-center position-absolute mb-8">
    <span class="spinner-border spinner-border-md align-middle"></span>
  </span>
</ng-template>
