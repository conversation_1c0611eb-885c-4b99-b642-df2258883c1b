<div class="d-flex" [ngClass]="{ 'd-none': (isFinishLoadingSubject | async) === false }">
  <button type="button" class="btn btn-filter me-3 {{ isActiveFilter ? 'active' : '' }}" (click)="handleOpen()">
    <span [inlineSVG]="'./assets/media/icons/ic_filter.svg'" class="svg-icon svg-icon-2"></span>
    Filter
    <span *ngIf="isActiveFilter" class="filter-is-active"></span>
  </button>
  <button *ngIf="isActiveFilter" type="button" class="btn btn-reset me-3" (click)="handleReset()">
    {{ resetLabel }}
  </button>
  <ng-container *ngIf="isOpenFilter">
    <ng-content></ng-content>
  </ng-container>
</div>

<div [ngClass]="{ 'd-none': (isFinishLoadingSubject | async) === true }">
  <ngx-skeleton-loader count="1" [theme]="{ 'border-radius': '5', height: '40px', width: '100px' }"></ngx-skeleton-loader>
</div>
