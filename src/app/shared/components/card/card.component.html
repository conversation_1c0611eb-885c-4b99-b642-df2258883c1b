<div class="card {{ cardClasses }} {{ border && 'border-card' }} animation animation-fade-in">
  <!--
    begin::card header
  -->
  <div *ngIf="header" class="card-header  {{ borderHeader && 'border-bottom-card' }} {{ cardHeaderClasses }}">
    <div class="card-title" *ngIf="cardHeaderTitle" [class.flex-column]="!!headerSubtitle">
      <h6 class="fw-bold m-0">{{ cardHeaderTitle }}</h6>
      <!--:: HEADER SUBTITLE -->
      <ng-container *ngIf="!!headerSubtitle">
        <span class="subtitle text-gray-700 mt-2" [innerHTML]="headerSubtitle"></span>
      </ng-container>
    </div>
    <ng-content select="[cardHeader]"></ng-content>
    <hr *ngIf="borderHeaderNotFull" class="mb-0 w-100" />
  </div>

  <!--
    begin::card body
  -->
  <div class="card-body {{ cardBodyClasses }}">
    <div class="card-title" *ngIf="titleInBody && cardHeaderTitle">
      <h5 class="fw-bold mb-7">{{ cardHeaderTitle }}</h5>
    </div>

    <ng-content select="[cardBody]"></ng-content>
  </div>
</div>
