import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { IListRejectReason } from '@pages/distributor/interfaces/distributor.interface';

@Component({
  selector: 'app-card-reject-reason',
  templateUrl: './card-reject-reason.component.html',
  styleUrls: ['./card-reject-reason.component.scss'],
})
export class CardRejectReasonComponent implements OnInit {
  constructor() {}
  @Input() title: string;
  @Input() bodyList: IListRejectReason[];
  @Input() bottomDescriptionText!: string;
  @Input() loading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  @Input() useListDesc = false;

  ngOnInit(): void {}
}
