<app-card [cardClasses]="'mt-7 mb-8 card-reject-reason'" cardHeaderTitle="{{title}}" [header]="true"
          cardBodyClasses="pt-0" cardHeaderClasses="py-5">
  <ng-container cardBody>
    <app-skeleton-text [count]=2 type="text" [isLoading]="loading">
      <ng-container *ngIf="!!bodyList.length"
                    [ngTemplateOutlet]="useListDesc ? listDescTpl : defaultListItem"
      ></ng-container>

      <ng-template #defaultListItem>
        <ul class="mb-0"><li *ngFor="let item of bodyList">{{item.enum_string}}</li></ul>
      </ng-template>

      <ng-template #listDescTpl>
        <ul class="list-unstyled mb-0">
          <li class="row mx-n3" *ngFor="let item of bodyList; last as last"
              [class.mb-2]="!last">
            <span class="text-gray-700 col-lg-auto col-12 min-w-lg-250px mw-lg-250px">{{item.enum_string}}</span>
            <span class="text-gray-900 col-12 col-lg min-w-lg-250px">: {{ item.desc }}</span>
          </li>
        </ul>
      </ng-template>
    </app-skeleton-text>

    <ng-container *ngIf="bottomDescriptionText">
      <div [innerHTML]="bottomDescriptionText"></div>
    </ng-container>

    <ng-content></ng-content>
  </ng-container>
</app-card>
