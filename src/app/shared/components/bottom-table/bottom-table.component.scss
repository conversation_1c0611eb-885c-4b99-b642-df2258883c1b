:host {
  .skeleton_group_bottom_table {
    display: flex;
    gap: 20px;
    margin-left: 10px;
  }


  ::ng-deep {

    .mdc-notched-outline > * {
      border: none !important;
    }

    .mat-mdc-paginator-container {
      padding: 0;
    }

    .mat-mdc-paginator-page-size-label,
    .mat-mdc-paginator-page-size-select {
      margin: 0;
    }

    @media(min-width: 992px) {
      .mat-mdc-paginator-container {
        justify-content: space-between;
      }

      .mat-mdc-paginator-range-actions {
        flex: 1;
        justify-content: flex-end;
      }

      .mat-mdc-paginator-range-label {
        margin-right: auto;
      }
    }

    // Pagination number
    .mat-mdc-paginator-range-actions .mat-custom-page-link {
      display: flex;
      justify-content: center;
      align-items: center;
      border: var(--kt-pagination-border-width) solid var(--kt-pagination-border-color)!important;
      border-radius: var(--kt-pagination-border-radius);
      font-weight: 400;
      font-size: var(--kt-pagination-font-size);
      color: var(--kt-pagination-color);
      padding: var(--kt-pagination-padding-x) var(--kt-pagination-padding-y)!important;
      height: var(--kt-pagination-item-height);
      min-width: var(--kt-pagination-item-width);

      &.active {
        background-color: var(--kt-pagination-active-bg);
        color: var(--kt-pagination-active-color);
        opacity: 1;
      }

      &:not(.last) {
        margin-right: 0.5rem;
      }

      &:hover {
        background-color: var(--kt-pagination-hover-bg);
      }
    }
  }
}
