import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-note-view',
  templateUrl: './note-view.component.html',
  styleUrls: ['./note-view.component.scss'],
})
export class NoteViewComponent implements OnInit {
  @Input() color: string;
  @Input() icon: string;
  @Input() classNoteView: string | undefined = 'mb-6';
  @Input() text: string | undefined;
  @Input() extraContent: boolean = false;

  textColor = [
    { color: 'warning', textColor: 'grey' },
    { color: 'info', textColor: 'gray-900' },
    { color: 'secondary', textColor: 'black' },
    { color: 'danger', textColor: 'danger' },
  ];

  constructor() {}

  ngOnInit(): void {}

  handleTextColor(color: string) {
    const getColor = this.textColor.find((val) => color === val.color);
    return getColor ? getColor.textColor : color;
  }
}
