import { EnumTypeBodyCardVerificationForm } from '../../pages/distributor/distributor-detail/detail-pending/detail-registration/distributor-data/distributor-data.data';

export interface IGenericPositionMap {
  lat: number;
  lng: number;
}

export interface IGenericUpdateData<T> {
  status: string | null;
  old_data: T;
  new_data: T;
}

export interface IGenericDetailUpdateData<T> {
  title: string;
  data: T[];
  status: string | null;
  map?: IGenericPositionMap;
}

export interface IGenericValueDisplay {
  value: string | boolean;
  display: string;
}

export interface IGenericValueDisplayDescription extends IGenericValueDisplay {
  description: string;
}

export interface IGenericValueDisplayDisable extends IGenericValueDisplay {
  disable: boolean;
}

export interface IGenericIdName {
  id: string;
  name: string;
}

export interface IGenericNameValue {
  name: string;
  value: string;
}

export interface IGenericNameUrl {
  name: string;
  url: string;
}

export interface IGenericSeqUrl {
  seq: number;
  url: string;
}

export interface IGenericKeyValue<T> {
  key: string;
  value: T;
}

export interface IGenericResponseSuccess {
  success: boolean | string;
  message: string;
}

export interface IGenericTextIcon {
  text: string;
  icon: string;
}

export interface IGenericUploadedDocument {
  uploaded_date: string;
  url: string[];
}

export interface IGenericActorModifier {
  actor_name: string;
  date: number; // epoch
}

export interface IGenericActor {
  actor: string;
  date: number; // epoch
}

export interface IGenericLabelValue<T = string> {
  label: string;
  value: T;
}

export interface IGenericLabelValueDescription extends IGenericLabelValue {
  description: string;
}

export interface IGenericLabelValueEnum extends IGenericLabelValue<string | number | any[]> {
  labelEnum?: string;
  typeValue?: string | EnumTypeBodyCardVerificationForm;
}

export interface IGenericCheckbox {
  checked: boolean;
  indeterminate?: boolean;
  selected?: boolean;
}

export interface IGenericEpochPeriod {
  period_start: number;
  period_end: number;
}

export interface IGenericCheckBox extends IGenericLabelValue {
  selected: boolean;
}
