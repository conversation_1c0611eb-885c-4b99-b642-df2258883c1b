import { OnDestroy, Directive } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Directive()
export abstract class BaseComponent implements OnDestroy {
  private destroy$?: Subject<void>;

  protected constructor() {}

  protected takeUntilDestroy = <T>() => {
    if (!this.destroy$) this.destroy$ = new Subject<void>(); // LAZY Subject
    return takeUntil<T>(this.destroy$);
  };

  ngOnDestroy(): void {
    if (this.destroy$) {
      this.destroy$.next();
      this.destroy$.complete();
    }
  }
}
